<?xml version="1.0" encoding="utf-8"?>
<configurationSnapshot xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="urn:schemas-microsoft-com:xml-wcfconfigurationsnapshot">
  <behaviors />
  <bindings>
    <binding digest="System.ServiceModel.Configuration.BasicHttpBindingElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089:&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data name=&quot;ServiceBinding1&quot; /&gt;" bindingType="basicHttpBinding" name="ServiceBinding1" />
  </bindings>
  <endpoints>
    <endpoint normalizedDigest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;http://127.0.0.1/nav/system/employee/soapserver&quot; binding=&quot;basicHttpBinding&quot; bindingConfiguration=&quot;ServiceBinding1&quot; contract=&quot;ServiceReference1.ServicePortType&quot; name=&quot;ServicePort1&quot; /&gt;" digest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;http://127.0.0.1/nav/system/employee/soapserver&quot; binding=&quot;basicHttpBinding&quot; bindingConfiguration=&quot;ServiceBinding1&quot; contract=&quot;ServiceReference1.ServicePortType&quot; name=&quot;ServicePort1&quot; /&gt;" contractName="ServiceReference1.ServicePortType" name="ServicePort1" />
  </endpoints>
</configurationSnapshot>