using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CtrEmail
{
    /// <summary>
    /// CtrOperationLog.xaml 的交互逻辑
    /// </summary>
    public partial class EmailLog : UserControl
    {
        public EmailLog()
        {
            InitializeComponent();

            init();
        }

        MainWindow mainWin;
        EmaillogModel emailModel;
        private void init()
        {
            App app = Application.Current as App;
            mainWin = app.mainWin;

            emailModel = new EmaillogModel();
            DataContext = emailModel;
            mainWin.emailDatagrid = emailDatagrid;

        }

        private void onDatagridSorting(object sender, DataGridSortingEventArgs e)
        {
            emailModel.Refresh();
        }
    }

    public class EmaillogModel : BaseViewModel
    {

        private ObservableCollection<ViewModel.EmaillogItem> _datagridSoruce;

        public ObservableCollection<ViewModel.EmaillogItem> DatagridSource
        {
            get
            {
                return _datagridSoruce;
            }
            set
            {
                if (_datagridSoruce != value)
                {
                    _datagridSoruce = value;
                    OnPropertyChanged("DatagridSource");
                }
            }
        }

        //private List<SysLogItem> _source;
        private ViewModel.EmaillogSource _source = new ViewModel.EmaillogSource();

        MainWindow mainWin;
        public EmaillogModel()
        {
            _currentPage = 1;

            App app = Application.Current as App;
            mainWin = app.mainWin;
            

            _totalPage = (int)Math.Ceiling(_source.Count * 1.0 / mainWin.pagesizeInt);

            _datagridSoruce = new ObservableCollection<ViewModel.EmaillogItem>();

            List<ViewModel.EmaillogItem> result = _source.Take(mainWin.pagesizeInt, 0).ToList();

            _datagridSoruce.Clear();

            _datagridSoruce.AddRange(result);

            _firstPageCommand = new DelegateCommand(FirstPageAction);

            _previousPageCommand = new DelegateCommand(PreviousPageAction);

            _nextPageCommand = new DelegateCommand(NextPageAction);

            _lastPageCommand = new DelegateCommand(LastPageAction);
        }

        public void Refresh()
        {
            if (CurrentPage < 1)
            {
                return;
            }
            List<ViewModel.EmaillogItem> result = new List<ViewModel.EmaillogItem>();
            _source.UpdateCount();
            _totalPage = (int)Math.Ceiling(_source.Count * 1.0 / mainWin.pagesizeInt);

            result = _source.Take(mainWin.pagesizeInt, (CurrentPage - 1) * mainWin.pagesizeInt).ToList();

            _datagridSoruce.Clear();

            _datagridSoruce.AddRange(result);
        }

        private void FirstPageAction()
        {
            CurrentPage = 1;

            var result = _source.Take(mainWin.pagesizeInt, 0).ToList();

            _datagridSoruce.Clear();

            _datagridSoruce.AddRange(result);
        }

        private void PreviousPageAction()
        {
            if (CurrentPage == 1)
            {
                return;
            }

            List<ViewModel.EmaillogItem> result = new List<ViewModel.EmaillogItem>();


            result = _source.Take(mainWin.pagesizeInt, (CurrentPage - 1) * mainWin.pagesizeInt).ToList();

            _datagridSoruce.Clear();

            _datagridSoruce.AddRange(result);

            CurrentPage--;
        }

        private void NextPageAction()
        {
            if (CurrentPage == _totalPage)
            {
                return;
            }

            List<ViewModel.EmaillogItem> result = new List<ViewModel.EmaillogItem>();

            result = _source.Take(mainWin.pagesizeInt, CurrentPage * mainWin.pagesizeInt).ToList();

            _datagridSoruce.Clear();

            _datagridSoruce.AddRange(result);

            CurrentPage++;
        }

        private void LastPageAction()
        {
            CurrentPage = TotalPage;

            int skipCount = (_totalPage - 1) * mainWin.pagesizeInt;
            int takeCount = _source.Count - skipCount;

            var result = _source.Take(takeCount, skipCount).ToList();

            _datagridSoruce.Clear();

            _datagridSoruce.AddRange(result);
        }
    }
}
