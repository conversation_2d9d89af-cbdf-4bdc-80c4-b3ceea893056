using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;

namespace CtrEmail
{
    class BackupTask
    {
        /*
        ManualResetEvent resetEvent = new ManualResetEvent(true);
        public BackupTask()
        {
            Task task = new Task(getCtrTask);
        }

        public void getCtrTask()
        {
            // ?????
        }

        public void stop()
        {
            resetEvent.Reset();
        }

        public void resume()
        {
            resetEvent.Set();
        }*/


        BackgroundWorker worker = new BackgroundWorker();
        public BackupTask(DoWorkEventHandler doworker, RunWorkerCompletedEventHandler workComplete)
        {
            worker.DoWork += doworker;
            worker.RunWorkerCompleted += workComplete;
        }


        public void Start(object arg)
        {
            worker.RunWorkerAsync(arg);
        }

        /*public void bgLogin(object sender, DoWorkEventArgs e)
        {
            try
            {
                int enscountInt = DbAdapter.GetInstance().GetEnsCount();
                if (enscountInt > 0)
                {
                    return;
                }
                ObservableCollection<ViewModel.EnsItem> ensList = DbAdapter.GetInstance().GetEns(enscountInt, 0);

                for (int i = 0; i < ensList.Count; i++)
                {
                    ViewModel.EnsItem ensServer = ensList[i];
                    if (string.IsNullOrEmpty(ensServer.Ip) || string.IsNullOrEmpty(ensServer.Username) || string.IsNullOrEmpty(ensServer.Password))
                    {
                        continue;
                    }
                    string sessionStr = Webservice.login(ensServer);
                    if (!string.IsNullOrEmpty(sessionStr))
                    {
                        //statusText.Visibility = Visibility.Collapsed;
                        DbAdapter.GetInstance().InsertLogin(ensServer);
                        ensServer.Session = sessionStr;

                        Webservice.GetControllers(ensServer, ref ensServer.controllerList);
                    }
                }
                e.Result = ensList; // 返回结果
            }
            catch (Exception)
            {

            }

        }*/

        private void bgLoginCallback(object sender, RunWorkerCompletedEventArgs e)
        {
            ObservableCollection<ViewModel.EnsItem> resultList = e.Result as ObservableCollection<ViewModel.EnsItem>;
            if (resultList == null)
                return;
            App app = Application.Current as App;
            MainWindow mainWin = app.mainWin;
            mainWin.ensList.Clear();

            foreach (ViewModel.EnsItem ensItem in resultList)
            {
                mainWin.ensList.Add(ensItem);
            }
        }
    }


    public enum TaskProcessEnum
    {
        CtrCard = 1,
    }
}
