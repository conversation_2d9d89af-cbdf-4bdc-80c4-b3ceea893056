#pragma checksum "..\..\SettingEmail.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "58C7BF5CC8DC5BD28711EE0AAD48C21BCE4137691440435A7EE02A71CAF1BF39"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using CtrEmail;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace CtrEmail {
    
    
    /// <summary>
    /// SettingEmail
    /// </summary>
    public partial class SettingEmail : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 42 "..\..\SettingEmail.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton apiRadio;
        
        #line default
        #line hidden
        
        
        #line 43 "..\..\SettingEmail.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton smtpRadio;
        
        #line default
        #line hidden
        
        
        #line 47 "..\..\SettingEmail.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock lblApiServer;
        
        #line default
        #line hidden
        
        
        #line 49 "..\..\SettingEmail.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox apiServerBox;
        
        #line default
        #line hidden
        
        
        #line 54 "..\..\SettingEmail.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock lblSmtpServer;
        
        #line default
        #line hidden
        
        
        #line 56 "..\..\SettingEmail.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox smtpServerBox;
        
        #line default
        #line hidden
        
        
        #line 58 "..\..\SettingEmail.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock lblSmtpPort;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\SettingEmail.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox smtpPortBox;
        
        #line default
        #line hidden
        
        
        #line 62 "..\..\SettingEmail.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock lblSmtpUsername;
        
        #line default
        #line hidden
        
        
        #line 64 "..\..\SettingEmail.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox smtpUsernameBox;
        
        #line default
        #line hidden
        
        
        #line 66 "..\..\SettingEmail.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock lblSmtpPassword;
        
        #line default
        #line hidden
        
        
        #line 68 "..\..\SettingEmail.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.PasswordBox smtpPasswordBox;
        
        #line default
        #line hidden
        
        
        #line 70 "..\..\SettingEmail.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock lblSmtpSsl;
        
        #line default
        #line hidden
        
        
        #line 72 "..\..\SettingEmail.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox smtpSslCheckBox;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\SettingEmail.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button testConnectionBtn;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\SettingEmail.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button saveBtn;
        
        #line default
        #line hidden
        
        
        #line 83 "..\..\SettingEmail.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button resetBtn;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/CtrEmail;component/settingemail.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\SettingEmail.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.apiRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 42 "..\..\SettingEmail.xaml"
            this.apiRadio.Checked += new System.Windows.RoutedEventHandler(this.OnSendModeChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.smtpRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 43 "..\..\SettingEmail.xaml"
            this.smtpRadio.Checked += new System.Windows.RoutedEventHandler(this.OnSendModeChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.lblApiServer = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.apiServerBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.lblSmtpServer = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.smtpServerBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.lblSmtpPort = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.smtpPortBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.lblSmtpUsername = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.smtpUsernameBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.lblSmtpPassword = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.smtpPasswordBox = ((System.Windows.Controls.PasswordBox)(target));
            return;
            case 13:
            this.lblSmtpSsl = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.smtpSslCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 15:
            this.testConnectionBtn = ((System.Windows.Controls.Button)(target));
            
            #line 77 "..\..\SettingEmail.xaml"
            this.testConnectionBtn.Click += new System.Windows.RoutedEventHandler(this.OnTestConnection);
            
            #line default
            #line hidden
            return;
            case 16:
            this.saveBtn = ((System.Windows.Controls.Button)(target));
            
            #line 82 "..\..\SettingEmail.xaml"
            this.saveBtn.Click += new System.Windows.RoutedEventHandler(this.OnSaveConfig);
            
            #line default
            #line hidden
            return;
            case 17:
            this.resetBtn = ((System.Windows.Controls.Button)(target));
            
            #line 84 "..\..\SettingEmail.xaml"
            this.resetBtn.Click += new System.Windows.RoutedEventHandler(this.OnResetConfig);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

