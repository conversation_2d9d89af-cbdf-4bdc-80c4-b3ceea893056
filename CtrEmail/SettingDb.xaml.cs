using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CtrEmail
{
    /// <summary>
    /// SettingDb.xaml �Ľ����߼�
    /// </summary>
    public partial class SettingDb : UserControl
    {
        public SettingDb()
        {
            InitializeComponent();
            initTextBox();
        }

        

        void initTextBox()
        {
            authCombo.Items.Add(new ComboBoxItem() { Content = "Windows�����֤" });
            authCombo.Items.Add(new ComboBoxItem() { Content = "SqlServer�����֤" });

            int tempInt = 0;
            string dbauthStr = Utils.XmlConfig.getValue("dbauth");
            Int32.TryParse(dbauthStr, out tempInt);
            authCombo.SelectedIndex = tempInt;

            this.ipBox.Text = Utils.XmlConfig.getValue("dbIp");
            this.usernameBox.Text = Utils.XmlConfig.getValue("dbUsername");
            this.passwordBox.Password = Utils.XmlConfig.getValue("dbPassword");

        }

        private void onOkBtn(object sender, RoutedEventArgs e)
        {
            try
            {
                string ipStr = this.ipBox.Text;
                string usernameStr = this.usernameBox.Text;
                string passwordStr = this.passwordBox.Password; // Utils.Util.md5(this.passwordBox.Password);

                Utils.XmlConfig.addValue("dbIp", ipStr);
                Utils.XmlConfig.addValue("dbUsername", usernameStr);
                Utils.XmlConfig.addValue("dbPassword", passwordStr);
                MessageBox.Show("���óɹ�");
                App app = Application.Current as App;
                //DbAdapter.GetInstance().SetDbInfo();    // �������ݿ���Ϣ
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }

        }

        private void onAuthCombo(object sender, SelectionChangedEventArgs e)
        {
            int indexInt = authCombo.SelectedIndex;
            if (indexInt < 0)
                return;
            Utils.XmlConfig.addValue("dbauth", indexInt.ToString());

            switch (indexInt)
            {
                case (int)DbAuthEnum.WindowsAuth:
                    usernameLab.Visibility = Visibility.Hidden;
                    usernameBox.Visibility = Visibility.Hidden;
                    passwordLab.Visibility = Visibility.Hidden;
                    passwordBox.Visibility = Visibility.Hidden;
                    break;
                case (int)DbAuthEnum.SqlAuth:
                    usernameLab.Visibility = Visibility.Visible;
                    usernameBox.Visibility = Visibility.Visible;
                    passwordLab.Visibility = Visibility.Visible;
                    passwordBox.Visibility = Visibility.Visible;
                    break;
            }
        }
    }

}
