using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CtrEmail.ViewModel
{

        public class EmaillogItem : INotifyPropertyChanged
        {
            public event PropertyChangedEventHandler PropertyChanged;

            private string _ensname;
            private string _ctrlocation;
            private string _emailfrom;
            private string _emailto;
            private string _emailtime;
            private string _offlinetime;

            public string Ensname
            {
                get { return _ensname; }
                set
                {
                    _ensname = value;
                    if (PropertyChanged != null)
                    {
                        PropertyChanged(this, new PropertyChangedEventArgs("ensname"));
                    }
                }
            }

            public string Ctrlocation
            {
                get { return _ctrlocation; }
                set
                {
                    _ctrlocation = value;
                    if (PropertyChanged != null)
                    {
                        PropertyChanged(this, new PropertyChangedEventArgs("ctrlocation"));
                    }
                }
            }

            public string Emailfrom
            {
                get { return _emailfrom; }
                set
                {
                    _emailfrom = value;
                    if (PropertyChanged != null)
                    {
                        PropertyChanged(this, new PropertyChangedEventArgs("emailfrom"));
                    }
                }
            }

            public string Emailto
            {
                get { return _emailto; }
                set
                {
                    _emailto = value;
                    if (PropertyChanged != null)
                    {
                        PropertyChanged(this, new PropertyChangedEventArgs("emailto"));
                    }
                }
            }

            public string Emailtime
            {
                get { return _emailtime; }
                set
                {
                    _emailtime = value;
                    if (PropertyChanged != null)
                    {
                        PropertyChanged(this, new PropertyChangedEventArgs("emailtime"));
                    }
                }
            }

            public string Offlinetime
            {
                get { return _offlinetime; }
                set
                {
                    _offlinetime = value;
                    if (PropertyChanged != null)
                    {
                        PropertyChanged(this, new PropertyChangedEventArgs("offlinetime"));
                    }
                }
            }


            public EmaillogItem(string ensname, string ctrlocation, string emailfrom, string emailto, string emailtime, string offlinetime)
            {
                Ensname = ensname;
                Ctrlocation = ctrlocation;
                Emailfrom = emailfrom;
                Emailto = emailto;
                Emailtime = emailtime;
                Offlinetime = offlinetime;
            }
        }
        public class EmaillogSource
        {
            public int Count;

            public EmaillogSource()
            {
                UpdateCount();
            }

            public ObservableCollection<EmaillogItem> Take(int itemcountInt, int skipInt)
            {
                UpdateCount();
                ObservableCollection<EmaillogItem> resultList = DbAdapter.GetInstance().GetEmaillog(itemcountInt, skipInt);
                return resultList;
            }

            public void UpdateCount()
            {
                Count = DbAdapter.GetInstance().GetEmaillogCount();
            }
        }

}
