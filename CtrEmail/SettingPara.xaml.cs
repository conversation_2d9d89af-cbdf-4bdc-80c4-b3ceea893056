using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CtrEmail
{
    /// <summary>
    /// SettingPara.xaml �Ľ����߼�
    /// </summary>
    public partial class SettingPara : UserControl
    {
        //private bool _cgcardauthBl = true;
        public SettingPara()
        {
            InitializeComponent();

            init();
            
        }
        MainWindow mainWin;
        private void init()
        {
            App app = Application.Current as App;
            mainWin = app.mainWin;

            this.pagesizeBox.Text = Utils.XmlConfig.getValue("pagesize");
            this.firstBox.Text = Utils.XmlConfig.getValue("firstemailminute");
            this.secondBox.Text = Utils.XmlConfig.getValue("secondemailminute");

            
            /*this.sendBox.Text = Utils.XmlConfig.getValue("receivemail");
            this.receiveBox.Text = Utils.XmlConfig.getValue("sendmail");
            string contentStr = Utils.XmlConfig.getValue("contentmail");

            FlowDocument doc = new FlowDocument();

            Paragraph p = new Paragraph();

            Run r = new Run(contentStr);
            p.Inlines.Add(r);//Run��Ԫ����ӵ�ParagraphԪ�ص�Inline

            doc.Blocks.Add(p);//Paragraph��Ԫ����ӵ����ĵ��Ŀ鼶Ԫ��

            contentBox.Document = doc;*/
        }



        private void onOkBtn(object sender, RoutedEventArgs e)
        {
            string pagesizeStr = pagesizeBox.Text;
            int pagesizeInt = 15;
            if (!Int32.TryParse(pagesizeStr,out pagesizeInt) || pagesizeInt <= 0)
            {
                MessageBox.Show("����Ƿ�");
                this.pagesizeBox.Focus();
                return;
            }

            string firstStr = firstBox.Text;
            int firstInt = 5;
            if (!Int32.TryParse(firstStr, out firstInt) || firstInt <= 0)
            {
                MessageBox.Show("����Ƿ�");
                this.firstBox.Focus();
                return;
            }

            string secondStr = secondBox.Text;
            int secondInt = 10;
            if (!Int32.TryParse(secondStr, out secondInt) || secondInt <= 0)
            {
                MessageBox.Show("����Ƿ�");
                this.secondBox.Focus();
                return;
            }

            /*string sendStr = sendBox.Text;

            if (sendStr.Trim().Equals(""))
            {
                MessageBox.Show("����Ƿ�");
                this.sendBox.Focus();
                return;
            }

            string receiveStr = receiveBox.Text;
            if (receiveStr.Trim().Equals(""))
            {
                MessageBox.Show("����Ƿ�");
                this.receiveBox.Focus();
                return;
            }

            TextRange textRange = new TextRange(contentBox.Document.ContentStart, contentBox.Document.ContentEnd);
            string contentStr = textRange.Text;*/

            Utils.XmlConfig.addValue("pagesize", pagesizeStr);
            Utils.XmlConfig.addValue("firstemailminute",firstStr );
            Utils.XmlConfig.addValue("secondemailminute",secondStr );
            //Utils.XmlConfig.addValue("receivemail", receiveStr);
            //Utils.XmlConfig.addValue("sendmail", receiveStr);
            //Utils.XmlConfig.addValue("contentmail", contentStr);

            mainWin.pagesizeInt = pagesizeInt;

            MessageBox.Show("���óɹ�");
        }
    }
}
