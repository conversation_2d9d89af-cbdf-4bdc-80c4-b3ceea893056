using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Threading;

namespace CtrEmail
{
    /// <summary>
    /// CtrList.xaml 的交互逻辑
    /// </summary>
    public partial class CtrList : UserControl
    {

        MainWindow mainWin;
        public CtrList()
        {
            InitializeComponent();

            init();
        }
        private CtrModel ctrModel;
        void init()
        {
            App app = Application.Current as App;
            mainWin = app.mainWin;

            ctrModel = new CtrModel();
            this.DataContext = ctrModel;
            mainWin.ctrDatagrid = ctrDatagrid;

        }

        public class CtrModel : BaseViewModel
        {


            private ObservableCollection<ViewModel.CtrItem> _datagridSoruce;

            public ObservableCollection<ViewModel.CtrItem> DatagridSource
            {
                get
                {
                    return _datagridSoruce;
                }
                set
                {
                    if (_datagridSoruce != value)
                    {
                        _datagridSoruce = value;
                        OnPropertyChanged("DatagridSource");
                    }
                }
            }

            //private List<SysLogItem> _source;
            private ViewModel.CtrSource _source = new ViewModel.CtrSource();

            MainWindow mainWin;
            public CtrModel()
            {
                _currentPage = 1;

                App app = Application.Current as App;
                mainWin = app.mainWin;


                _totalPage = (int)Math.Ceiling(_source.Count * 1.0 / mainWin.pagesizeInt);

                _datagridSoruce = new ObservableCollection<ViewModel.CtrItem>();

                List<ViewModel.CtrItem> result = _source.Take(mainWin.pagesizeInt, 0).ToList();

                _datagridSoruce.Clear();

                _datagridSoruce.AddRange(result);

                _firstPageCommand = new DelegateCommand(FirstPageAction);

                _previousPageCommand = new DelegateCommand(PreviousPageAction);

                _nextPageCommand = new DelegateCommand(NextPageAction);

                _lastPageCommand = new DelegateCommand(LastPageAction);
            }

            private void FirstPageAction()
            {
                CurrentPage = 1;
                _source.UpdateCount();
                var result = _source.Take(mainWin.pagesizeInt, 0).ToList();

                _datagridSoruce.Clear();

                _datagridSoruce.AddRange(result);
            }

            private void PreviousPageAction()
            {
                if (CurrentPage == 1)
                {
                    return;
                }

                List<ViewModel.CtrItem> result = new List<ViewModel.CtrItem>();

                int skipnumInt = (CurrentPage - 2) * mainWin.pagesizeInt;
                if (skipnumInt < 0)
                    skipnumInt = 0;
                _source.UpdateCount();
                result = _source.Take(mainWin.pagesizeInt, skipnumInt).ToList();

                _datagridSoruce.Clear();

                _datagridSoruce.AddRange(result);

                CurrentPage--;
            }

            private void NextPageAction()
            {
                if (CurrentPage == _totalPage)
                {
                    return;
                }

                List<ViewModel.CtrItem> result = new List<ViewModel.CtrItem>();
                _source.UpdateCount();
                result = _source.Take(mainWin.pagesizeInt, CurrentPage * mainWin.pagesizeInt).ToList();

                _datagridSoruce.Clear();

                _datagridSoruce.AddRange(result);

                CurrentPage++;
            }

            public void Refresh()
            {
                if (CurrentPage <1)
                {
                    return;
                }
                List<ViewModel.CtrItem> result = new List<ViewModel.CtrItem>();
                _source.UpdateCount();
                _totalPage = (int)Math.Ceiling(_source.Count * 1.0 / mainWin.pagesizeInt);

                result = _source.Take(mainWin.pagesizeInt, (CurrentPage-1) * mainWin.pagesizeInt).ToList();

                _datagridSoruce.Clear();

                _datagridSoruce.AddRange(result);
            }

            private void LastPageAction()
            {
                CurrentPage = TotalPage;

                int skipCount = (_totalPage - 1) * mainWin.pagesizeInt;
                int takeCount = _source.Count - skipCount;
                _source.UpdateCount();
                var result = _source.Take(takeCount, skipCount).ToList();

                _datagridSoruce.Clear();

                _datagridSoruce.AddRange(result);
            }
        }

        private void onDatagridSorting(object sender, DataGridSortingEventArgs e)
        {
            ctrModel.Refresh();
        }
    }



}
