using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using System.Windows.Forms;

namespace CtrEmail.Utils
{
    public class XmlConfig
    {
        //������õ�xml�ļ�λ��
        private static string xmlFile = System.IO.Path.GetDirectoryName(Application.ExecutablePath) + "/conf.xml";

        public static void addValue(string _nodeName, string value)
        {
            //��ʼ��xml����
            XmlDocument xmldoc = new XmlDocument();
            //���������ļ�
            try
            {
                xmldoc.Load(xmlFile);
            }
            catch (Exception)
            {
                XmlDeclaration dec = xmldoc.CreateXmlDeclaration("1.0", "GB2312", null);
                xmldoc.AppendChild(dec);
                XmlElement XMLroot = xmldoc.CreateElement("root");
                xmldoc.AppendChild(XMLroot);
            }

            //��ø��ڵ�
            XmlNode root = xmldoc.DocumentElement;

            bool findBl = false;
            for (int i = 0; i < root.ChildNodes.Count; i++)
            {
                string xmlnameStr = root.ChildNodes[i].Name.Substring(1);
                if (string.IsNullOrEmpty(xmlnameStr) || xmlnameStr.Length <= 1)
                    continue;

                //���Ҫѡ��Ľڵ�
                if (_nodeName.Equals(Util.decrypt(xmlnameStr)))
                {
                    root.ChildNodes[i].InnerText = Util.encrypt(value);
                    findBl = true;
                    break;
                }
            }

            if (!findBl)
            {
                string nodenameStr = "A" + Util.encrypt(_nodeName).Trim('=');
                XmlElement aimNode = xmldoc.CreateElement(nodenameStr);
                aimNode.InnerText = Util.encrypt(value);
                root.AppendChild(aimNode);
            }

            //��������е����ݵ��ļ�
            xmldoc.Save(xmlFile);
        }

        public static string getValue(string nodeName)
        {
            try
            {
                //��ʼ��xml����
                XmlDocument xmldoc = new XmlDocument();
                //���������ļ�
                xmldoc.Load(xmlFile);
                //��ø��ڵ�
                XmlNode root = xmldoc.DocumentElement;

                foreach (XmlNode aimNode in root.ChildNodes)
                {
                    string nameStr = Util.decrypt(aimNode.Name.Substring(1));
                    if (nodeName.Equals(nameStr))
                    {
                        string valueStr = Util.decrypt(aimNode.InnerText);
                        return valueStr;
                    }
                }
            }
            catch (Exception)
            {
            }
            return "";
        }
    }
}
