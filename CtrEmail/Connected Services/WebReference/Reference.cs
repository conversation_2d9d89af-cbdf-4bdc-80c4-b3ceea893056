//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

namespace CtrEmail.WebReference {
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.ServiceContractAttribute(Namespace="urn:wsdl", ConfigurationName="WebReference.ServicePortType")]
    public interface ServicePortType {
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(Style=System.ServiceModel.OperationFormatStyle.Rpc, SupportFaults=true, Use=System.ServiceModel.OperationFormatUse.Encoded)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="loginReturn")]
        string login(string username, string password);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="loginReturn")]
        System.Threading.Tasks.Task<string> loginAsync(string username, string password);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(Style=System.ServiceModel.OperationFormatStyle.Rpc, SupportFaults=true, Use=System.ServiceModel.OperationFormatUse.Encoded)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="insertReserveCardsReturn")]
        string insertReserveCards(string sessionid, string cards, string ip, string userid);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="insertReserveCardsReturn")]
        System.Threading.Tasks.Task<string> insertReserveCardsAsync(string sessionid, string cards, string ip, string userid);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(Style=System.ServiceModel.OperationFormatStyle.Rpc, SupportFaults=true, Use=System.ServiceModel.OperationFormatUse.Encoded)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="searchReserveCardsReturn")]
        string searchReserveCards(string sessionid, string cards);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="searchReserveCardsReturn")]
        System.Threading.Tasks.Task<string> searchReserveCardsAsync(string sessionid, string cards);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(Style=System.ServiceModel.OperationFormatStyle.Rpc, SupportFaults=true, Use=System.ServiceModel.OperationFormatUse.Encoded)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="deleteReserveCardsReturn")]
        string deleteReserveCards(string sessionid, string cards);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="deleteReserveCardsReturn")]
        System.Threading.Tasks.Task<string> deleteReserveCardsAsync(string sessionid, string cards);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(Style=System.ServiceModel.OperationFormatStyle.Rpc, SupportFaults=true, Use=System.ServiceModel.OperationFormatUse.Encoded)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="insertPublicDoorsReturn")]
        string insertPublicDoors(string sessionid, string doorInfo, string ip, string userid);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="insertPublicDoorsReturn")]
        System.Threading.Tasks.Task<string> insertPublicDoorsAsync(string sessionid, string doorInfo, string ip, string userid);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(Style=System.ServiceModel.OperationFormatStyle.Rpc, SupportFaults=true, Use=System.ServiceModel.OperationFormatUse.Encoded)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="updatePublicDoorsReturn")]
        string updatePublicDoors(string sessionid, string doorInfo, string ip, string userid);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="updatePublicDoorsReturn")]
        System.Threading.Tasks.Task<string> updatePublicDoorsAsync(string sessionid, string doorInfo, string ip, string userid);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(Style=System.ServiceModel.OperationFormatStyle.Rpc, SupportFaults=true, Use=System.ServiceModel.OperationFormatUse.Encoded)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="searchPublicDoorsReturn")]
        string searchPublicDoors(string sessionid, string doorInfo);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="searchPublicDoorsReturn")]
        System.Threading.Tasks.Task<string> searchPublicDoorsAsync(string sessionid, string doorInfo);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(Style=System.ServiceModel.OperationFormatStyle.Rpc, SupportFaults=true, Use=System.ServiceModel.OperationFormatUse.Encoded)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="deletePublicDoorsReturn")]
        string deletePublicDoors(string sessionid, string doorInfo, string ip, string userid);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="deletePublicDoorsReturn")]
        System.Threading.Tasks.Task<string> deletePublicDoorsAsync(string sessionid, string doorInfo, string ip, string userid);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(Style=System.ServiceModel.OperationFormatStyle.Rpc, SupportFaults=true, Use=System.ServiceModel.OperationFormatUse.Encoded)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="updateDoorsReturn")]
        string updateDoors(string sessionid, string doorInfo, string ip, string userid);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="updateDoorsReturn")]
        System.Threading.Tasks.Task<string> updateDoorsAsync(string sessionid, string doorInfo, string ip, string userid);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(Style=System.ServiceModel.OperationFormatStyle.Rpc, SupportFaults=true, Use=System.ServiceModel.OperationFormatUse.Encoded)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="insertEmployeesReturn")]
        string insertEmployees(string sessionid, string employees);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="insertEmployeesReturn")]
        System.Threading.Tasks.Task<string> insertEmployeesAsync(string sessionid, string employees);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(Style=System.ServiceModel.OperationFormatStyle.Rpc, SupportFaults=true, Use=System.ServiceModel.OperationFormatUse.Encoded)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="deleteEmployeesReturn")]
        string deleteEmployees(string sessionid, string employees);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="deleteEmployeesReturn")]
        System.Threading.Tasks.Task<string> deleteEmployeesAsync(string sessionid, string employees);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(Style=System.ServiceModel.OperationFormatStyle.Rpc, SupportFaults=true, Use=System.ServiceModel.OperationFormatUse.Encoded)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="updateEmployeesReturn")]
        string updateEmployees(string sessionid, string employees);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="updateEmployeesReturn")]
        System.Threading.Tasks.Task<string> updateEmployeesAsync(string sessionid, string employees);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(Style=System.ServiceModel.OperationFormatStyle.Rpc, SupportFaults=true, Use=System.ServiceModel.OperationFormatUse.Encoded)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="searchEmployeesReturn")]
        string searchEmployees(string sessionid, string cards);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="searchEmployeesReturn")]
        System.Threading.Tasks.Task<string> searchEmployeesAsync(string sessionid, string cards);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(Style=System.ServiceModel.OperationFormatStyle.Rpc, SupportFaults=true, Use=System.ServiceModel.OperationFormatUse.Encoded)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="insertCardsReturn")]
        string insertCards(string sessionid, string cards);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="insertCardsReturn")]
        System.Threading.Tasks.Task<string> insertCardsAsync(string sessionid, string cards);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(Style=System.ServiceModel.OperationFormatStyle.Rpc, SupportFaults=true, Use=System.ServiceModel.OperationFormatUse.Encoded)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="updateCardsReturn")]
        string updateCards(string sessionid, string cards);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="updateCardsReturn")]
        System.Threading.Tasks.Task<string> updateCardsAsync(string sessionid, string cards);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(Style=System.ServiceModel.OperationFormatStyle.Rpc, SupportFaults=true, Use=System.ServiceModel.OperationFormatUse.Encoded)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="deleteCardsReturn")]
        string deleteCards(string sessionid, string cards);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="deleteCardsReturn")]
        System.Threading.Tasks.Task<string> deleteCardsAsync(string sessionid, string cards);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(Style=System.ServiceModel.OperationFormatStyle.Rpc, SupportFaults=true, Use=System.ServiceModel.OperationFormatUse.Encoded)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="searchCardsReturn")]
        string searchCards(string sessionid, string cards);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="searchCardsReturn")]
        System.Threading.Tasks.Task<string> searchCardsAsync(string sessionid, string cards);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(Style=System.ServiceModel.OperationFormatStyle.Rpc, SupportFaults=true, Use=System.ServiceModel.OperationFormatUse.Encoded)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="insertDeptsReturn")]
        string insertDepts(string sessionid, string depts);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="insertDeptsReturn")]
        System.Threading.Tasks.Task<string> insertDeptsAsync(string sessionid, string depts);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(Style=System.ServiceModel.OperationFormatStyle.Rpc, SupportFaults=true, Use=System.ServiceModel.OperationFormatUse.Encoded)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="updateDeptsReturn")]
        string updateDepts(string sessionid, string depts);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="updateDeptsReturn")]
        System.Threading.Tasks.Task<string> updateDeptsAsync(string sessionid, string depts);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(Style=System.ServiceModel.OperationFormatStyle.Rpc, SupportFaults=true, Use=System.ServiceModel.OperationFormatUse.Encoded)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="deleteDeptsReturn")]
        string deleteDepts(string sessionid, string depts);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="deleteDeptsReturn")]
        System.Threading.Tasks.Task<string> deleteDeptsAsync(string sessionid, string depts);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(Style=System.ServiceModel.OperationFormatStyle.Rpc, SupportFaults=true, Use=System.ServiceModel.OperationFormatUse.Encoded)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="searchDeptsReturn")]
        string searchDepts(string sessionid, string depts);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="searchDeptsReturn")]
        System.Threading.Tasks.Task<string> searchDeptsAsync(string sessionid, string depts);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(Style=System.ServiceModel.OperationFormatStyle.Rpc, SupportFaults=true, Use=System.ServiceModel.OperationFormatUse.Encoded)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="getCardsDoorsReturn")]
        string getCardsDoors(string sessionid, string card);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="getCardsDoorsReturn")]
        System.Threading.Tasks.Task<string> getCardsDoorsAsync(string sessionid, string card);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(Style=System.ServiceModel.OperationFormatStyle.Rpc, SupportFaults=true, Use=System.ServiceModel.OperationFormatUse.Encoded)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="searchCardsDoorsReturn")]
        string searchCardsDoors(string sessionid, string depts, string leaders, string posts);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="searchCardsDoorsReturn")]
        System.Threading.Tasks.Task<string> searchCardsDoorsAsync(string sessionid, string depts, string leaders, string posts);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(Style=System.ServiceModel.OperationFormatStyle.Rpc, SupportFaults=true, Use=System.ServiceModel.OperationFormatUse.Encoded)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="asyncSetCardsDoorsReturn")]
        string asyncSetCardsDoors(string sessionid, string cards, int commandno);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="asyncSetCardsDoorsReturn")]
        System.Threading.Tasks.Task<string> asyncSetCardsDoorsAsync(string sessionid, string cards, int commandno);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(Style=System.ServiceModel.OperationFormatStyle.Rpc, SupportFaults=true, Use=System.ServiceModel.OperationFormatUse.Encoded)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="setCardsDoorsReturn")]
        string setCardsDoors(string sessionid, string cards);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="setCardsDoorsReturn")]
        System.Threading.Tasks.Task<string> setCardsDoorsAsync(string sessionid, string cards);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(Style=System.ServiceModel.OperationFormatStyle.Rpc, SupportFaults=true, Use=System.ServiceModel.OperationFormatUse.Encoded)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="normalCardeventReportReturn")]
        string normalCardeventReport(string sessionid, string condition, string filetype);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="normalCardeventReportReturn")]
        System.Threading.Tasks.Task<string> normalCardeventReportAsync(string sessionid, string condition, string filetype);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(Style=System.ServiceModel.OperationFormatStyle.Rpc, SupportFaults=true, Use=System.ServiceModel.OperationFormatUse.Encoded)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="abnormalCardeventReportReturn")]
        string abnormalCardeventReport(string sessionid, string condition, int filetype);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="abnormalCardeventReportReturn")]
        System.Threading.Tasks.Task<string> abnormalCardeventReportAsync(string sessionid, string condition, int filetype);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(Style=System.ServiceModel.OperationFormatStyle.Rpc, SupportFaults=true, Use=System.ServiceModel.OperationFormatUse.Encoded)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="doorRelationReportReturn")]
        string doorRelationReport(string sessionid, string condition, int filetype);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="doorRelationReportReturn")]
        System.Threading.Tasks.Task<string> doorRelationReportAsync(string sessionid, string condition, int filetype);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(Style=System.ServiceModel.OperationFormatStyle.Rpc, SupportFaults=true, Use=System.ServiceModel.OperationFormatUse.Encoded)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="abnormalEmployeeeventReportReturn")]
        string abnormalEmployeeeventReport(string sessionid, string condition, int filetype);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="abnormalEmployeeeventReportReturn")]
        System.Threading.Tasks.Task<string> abnormalEmployeeeventReportAsync(string sessionid, string condition, int filetype);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(Style=System.ServiceModel.OperationFormatStyle.Rpc, SupportFaults=true, Use=System.ServiceModel.OperationFormatUse.Encoded)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="employeeAuthReportReturn")]
        string employeeAuthReport(string sessionid, string condition, int filetype);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="employeeAuthReportReturn")]
        System.Threading.Tasks.Task<string> employeeAuthReportAsync(string sessionid, string condition, int filetype);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(Style=System.ServiceModel.OperationFormatStyle.Rpc, SupportFaults=true, Use=System.ServiceModel.OperationFormatUse.Encoded)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="doorReportReturn")]
        string doorReport(string sessionid, string condition, int filetype);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="doorReportReturn")]
        System.Threading.Tasks.Task<string> doorReportAsync(string sessionid, string condition, int filetype);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(Style=System.ServiceModel.OperationFormatStyle.Rpc, SupportFaults=true, Use=System.ServiceModel.OperationFormatUse.Encoded)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="accessLabReportReturn")]
        string accessLabReport(string sessionid, string condition, int filetype);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="accessLabReportReturn")]
        System.Threading.Tasks.Task<string> accessLabReportAsync(string sessionid, string condition, int filetype);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(Style=System.ServiceModel.OperationFormatStyle.Rpc, SupportFaults=true, Use=System.ServiceModel.OperationFormatUse.Encoded)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="employeeDoorsReporttReturn")]
        string employeeDoorsReport(string sessionid, string condition, int filetype);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="employeeDoorsReporttReturn")]
        System.Threading.Tasks.Task<string> employeeDoorsReportAsync(string sessionid, string condition, int filetype);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(Style=System.ServiceModel.OperationFormatStyle.Rpc, SupportFaults=true, Use=System.ServiceModel.OperationFormatUse.Encoded)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="heartBeatReturn")]
        string heartBeat(string sessionid);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="heartBeatReturn")]
        System.Threading.Tasks.Task<string> heartBeatAsync(string sessionid);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(Style=System.ServiceModel.OperationFormatStyle.Rpc, SupportFaults=true, Use=System.ServiceModel.OperationFormatUse.Encoded)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="searchDoorsCardsReturn")]
        string searchDoorsCards(string sessionid, string doors);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:ServiceAction", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="searchDoorsCardsReturn")]
        System.Threading.Tasks.Task<string> searchDoorsCardsAsync(string sessionid, string doors);
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public interface ServicePortTypeChannel : CtrEmail.WebReference.ServicePortType, System.ServiceModel.IClientChannel {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public partial class ServicePortTypeClient : System.ServiceModel.ClientBase<CtrEmail.WebReference.ServicePortType>, CtrEmail.WebReference.ServicePortType {
        
        public ServicePortTypeClient() {
        }
        
        public ServicePortTypeClient(string endpointConfigurationName) : 
                base(endpointConfigurationName) {
        }
        
        public ServicePortTypeClient(string endpointConfigurationName, string remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public ServicePortTypeClient(string endpointConfigurationName, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public ServicePortTypeClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress) {
        }
        
        public string login(string username, string password) {
            return base.Channel.login(username, password);
        }
        
        public System.Threading.Tasks.Task<string> loginAsync(string username, string password) {
            return base.Channel.loginAsync(username, password);
        }
        
        public string insertReserveCards(string sessionid, string cards, string ip, string userid) {
            return base.Channel.insertReserveCards(sessionid, cards, ip, userid);
        }
        
        public System.Threading.Tasks.Task<string> insertReserveCardsAsync(string sessionid, string cards, string ip, string userid) {
            return base.Channel.insertReserveCardsAsync(sessionid, cards, ip, userid);
        }
        
        public string searchReserveCards(string sessionid, string cards) {
            return base.Channel.searchReserveCards(sessionid, cards);
        }
        
        public System.Threading.Tasks.Task<string> searchReserveCardsAsync(string sessionid, string cards) {
            return base.Channel.searchReserveCardsAsync(sessionid, cards);
        }
        
        public string deleteReserveCards(string sessionid, string cards) {
            return base.Channel.deleteReserveCards(sessionid, cards);
        }
        
        public System.Threading.Tasks.Task<string> deleteReserveCardsAsync(string sessionid, string cards) {
            return base.Channel.deleteReserveCardsAsync(sessionid, cards);
        }
        
        public string insertPublicDoors(string sessionid, string doorInfo, string ip, string userid) {
            return base.Channel.insertPublicDoors(sessionid, doorInfo, ip, userid);
        }
        
        public System.Threading.Tasks.Task<string> insertPublicDoorsAsync(string sessionid, string doorInfo, string ip, string userid) {
            return base.Channel.insertPublicDoorsAsync(sessionid, doorInfo, ip, userid);
        }
        
        public string updatePublicDoors(string sessionid, string doorInfo, string ip, string userid) {
            return base.Channel.updatePublicDoors(sessionid, doorInfo, ip, userid);
        }
        
        public System.Threading.Tasks.Task<string> updatePublicDoorsAsync(string sessionid, string doorInfo, string ip, string userid) {
            return base.Channel.updatePublicDoorsAsync(sessionid, doorInfo, ip, userid);
        }
        
        public string searchPublicDoors(string sessionid, string doorInfo) {
            return base.Channel.searchPublicDoors(sessionid, doorInfo);
        }
        
        public System.Threading.Tasks.Task<string> searchPublicDoorsAsync(string sessionid, string doorInfo) {
            return base.Channel.searchPublicDoorsAsync(sessionid, doorInfo);
        }
        
        public string deletePublicDoors(string sessionid, string doorInfo, string ip, string userid) {
            return base.Channel.deletePublicDoors(sessionid, doorInfo, ip, userid);
        }
        
        public System.Threading.Tasks.Task<string> deletePublicDoorsAsync(string sessionid, string doorInfo, string ip, string userid) {
            return base.Channel.deletePublicDoorsAsync(sessionid, doorInfo, ip, userid);
        }
        
        public string updateDoors(string sessionid, string doorInfo, string ip, string userid) {
            return base.Channel.updateDoors(sessionid, doorInfo, ip, userid);
        }
        
        public System.Threading.Tasks.Task<string> updateDoorsAsync(string sessionid, string doorInfo, string ip, string userid) {
            return base.Channel.updateDoorsAsync(sessionid, doorInfo, ip, userid);
        }
        
        public string insertEmployees(string sessionid, string employees) {
            return base.Channel.insertEmployees(sessionid, employees);
        }
        
        public System.Threading.Tasks.Task<string> insertEmployeesAsync(string sessionid, string employees) {
            return base.Channel.insertEmployeesAsync(sessionid, employees);
        }
        
        public string deleteEmployees(string sessionid, string employees) {
            return base.Channel.deleteEmployees(sessionid, employees);
        }
        
        public System.Threading.Tasks.Task<string> deleteEmployeesAsync(string sessionid, string employees) {
            return base.Channel.deleteEmployeesAsync(sessionid, employees);
        }
        
        public string updateEmployees(string sessionid, string employees) {
            return base.Channel.updateEmployees(sessionid, employees);
        }
        
        public System.Threading.Tasks.Task<string> updateEmployeesAsync(string sessionid, string employees) {
            return base.Channel.updateEmployeesAsync(sessionid, employees);
        }
        
        public string searchEmployees(string sessionid, string cards) {
            return base.Channel.searchEmployees(sessionid, cards);
        }
        
        public System.Threading.Tasks.Task<string> searchEmployeesAsync(string sessionid, string cards) {
            return base.Channel.searchEmployeesAsync(sessionid, cards);
        }
        
        public string insertCards(string sessionid, string cards) {
            return base.Channel.insertCards(sessionid, cards);
        }
        
        public System.Threading.Tasks.Task<string> insertCardsAsync(string sessionid, string cards) {
            return base.Channel.insertCardsAsync(sessionid, cards);
        }
        
        public string updateCards(string sessionid, string cards) {
            return base.Channel.updateCards(sessionid, cards);
        }
        
        public System.Threading.Tasks.Task<string> updateCardsAsync(string sessionid, string cards) {
            return base.Channel.updateCardsAsync(sessionid, cards);
        }
        
        public string deleteCards(string sessionid, string cards) {
            return base.Channel.deleteCards(sessionid, cards);
        }
        
        public System.Threading.Tasks.Task<string> deleteCardsAsync(string sessionid, string cards) {
            return base.Channel.deleteCardsAsync(sessionid, cards);
        }
        
        public string searchCards(string sessionid, string cards) {
            return base.Channel.searchCards(sessionid, cards);
        }
        
        public System.Threading.Tasks.Task<string> searchCardsAsync(string sessionid, string cards) {
            return base.Channel.searchCardsAsync(sessionid, cards);
        }
        
        public string insertDepts(string sessionid, string depts) {
            return base.Channel.insertDepts(sessionid, depts);
        }
        
        public System.Threading.Tasks.Task<string> insertDeptsAsync(string sessionid, string depts) {
            return base.Channel.insertDeptsAsync(sessionid, depts);
        }
        
        public string updateDepts(string sessionid, string depts) {
            return base.Channel.updateDepts(sessionid, depts);
        }
        
        public System.Threading.Tasks.Task<string> updateDeptsAsync(string sessionid, string depts) {
            return base.Channel.updateDeptsAsync(sessionid, depts);
        }
        
        public string deleteDepts(string sessionid, string depts) {
            return base.Channel.deleteDepts(sessionid, depts);
        }
        
        public System.Threading.Tasks.Task<string> deleteDeptsAsync(string sessionid, string depts) {
            return base.Channel.deleteDeptsAsync(sessionid, depts);
        }
        
        public string searchDepts(string sessionid, string depts) {
            return base.Channel.searchDepts(sessionid, depts);
        }
        
        public System.Threading.Tasks.Task<string> searchDeptsAsync(string sessionid, string depts) {
            return base.Channel.searchDeptsAsync(sessionid, depts);
        }
        
        public string getCardsDoors(string sessionid, string card) {
            return base.Channel.getCardsDoors(sessionid, card);
        }
        
        public System.Threading.Tasks.Task<string> getCardsDoorsAsync(string sessionid, string card) {
            return base.Channel.getCardsDoorsAsync(sessionid, card);
        }
        
        public string searchCardsDoors(string sessionid, string depts, string leaders, string posts) {
            return base.Channel.searchCardsDoors(sessionid, depts, leaders, posts);
        }
        
        public System.Threading.Tasks.Task<string> searchCardsDoorsAsync(string sessionid, string depts, string leaders, string posts) {
            return base.Channel.searchCardsDoorsAsync(sessionid, depts, leaders, posts);
        }
        
        public string asyncSetCardsDoors(string sessionid, string cards, int commandno) {
            return base.Channel.asyncSetCardsDoors(sessionid, cards, commandno);
        }
        
        public System.Threading.Tasks.Task<string> asyncSetCardsDoorsAsync(string sessionid, string cards, int commandno) {
            return base.Channel.asyncSetCardsDoorsAsync(sessionid, cards, commandno);
        }
        
        public string setCardsDoors(string sessionid, string cards) {
            return base.Channel.setCardsDoors(sessionid, cards);
        }
        
        public System.Threading.Tasks.Task<string> setCardsDoorsAsync(string sessionid, string cards) {
            return base.Channel.setCardsDoorsAsync(sessionid, cards);
        }
        
        public string normalCardeventReport(string sessionid, string condition, string filetype) {
            return base.Channel.normalCardeventReport(sessionid, condition, filetype);
        }
        
        public System.Threading.Tasks.Task<string> normalCardeventReportAsync(string sessionid, string condition, string filetype) {
            return base.Channel.normalCardeventReportAsync(sessionid, condition, filetype);
        }
        
        public string abnormalCardeventReport(string sessionid, string condition, int filetype) {
            return base.Channel.abnormalCardeventReport(sessionid, condition, filetype);
        }
        
        public System.Threading.Tasks.Task<string> abnormalCardeventReportAsync(string sessionid, string condition, int filetype) {
            return base.Channel.abnormalCardeventReportAsync(sessionid, condition, filetype);
        }
        
        public string doorRelationReport(string sessionid, string condition, int filetype) {
            return base.Channel.doorRelationReport(sessionid, condition, filetype);
        }
        
        public System.Threading.Tasks.Task<string> doorRelationReportAsync(string sessionid, string condition, int filetype) {
            return base.Channel.doorRelationReportAsync(sessionid, condition, filetype);
        }
        
        public string abnormalEmployeeeventReport(string sessionid, string condition, int filetype) {
            return base.Channel.abnormalEmployeeeventReport(sessionid, condition, filetype);
        }
        
        public System.Threading.Tasks.Task<string> abnormalEmployeeeventReportAsync(string sessionid, string condition, int filetype) {
            return base.Channel.abnormalEmployeeeventReportAsync(sessionid, condition, filetype);
        }
        
        public string employeeAuthReport(string sessionid, string condition, int filetype) {
            return base.Channel.employeeAuthReport(sessionid, condition, filetype);
        }
        
        public System.Threading.Tasks.Task<string> employeeAuthReportAsync(string sessionid, string condition, int filetype) {
            return base.Channel.employeeAuthReportAsync(sessionid, condition, filetype);
        }
        
        public string doorReport(string sessionid, string condition, int filetype) {
            return base.Channel.doorReport(sessionid, condition, filetype);
        }
        
        public System.Threading.Tasks.Task<string> doorReportAsync(string sessionid, string condition, int filetype) {
            return base.Channel.doorReportAsync(sessionid, condition, filetype);
        }
        
        public string accessLabReport(string sessionid, string condition, int filetype) {
            return base.Channel.accessLabReport(sessionid, condition, filetype);
        }
        
        public System.Threading.Tasks.Task<string> accessLabReportAsync(string sessionid, string condition, int filetype) {
            return base.Channel.accessLabReportAsync(sessionid, condition, filetype);
        }
        
        public string employeeDoorsReport(string sessionid, string condition, int filetype) {
            return base.Channel.employeeDoorsReport(sessionid, condition, filetype);
        }
        
        public System.Threading.Tasks.Task<string> employeeDoorsReportAsync(string sessionid, string condition, int filetype) {
            return base.Channel.employeeDoorsReportAsync(sessionid, condition, filetype);
        }
        
        public string heartBeat(string sessionid) {
            return base.Channel.heartBeat(sessionid);
        }
        
        public System.Threading.Tasks.Task<string> heartBeatAsync(string sessionid) {
            return base.Channel.heartBeatAsync(sessionid);
        }
        
        public string searchDoorsCards(string sessionid, string doors) {
            return base.Channel.searchDoorsCards(sessionid, doors);
        }
        
        public System.Threading.Tasks.Task<string> searchDoorsCardsAsync(string sessionid, string doors) {
            return base.Channel.searchDoorsCardsAsync(sessionid, doors);
        }
    }
}
