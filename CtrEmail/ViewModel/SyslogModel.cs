using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CtrEmail.ViewModel
{
    // 代表数据表，从数据库中得到数据
    public class EnsloginlogItem : INotifyPropertyChanged
    {
        public event PropertyChangedEventHandler PropertyChanged;

        private string _logtime;
        private string _username;
        private string _ensip;
        private string _content;

        public string Logtime
        {
            get { return _logtime; }
            set
            {
                _logtime = value;
                if(PropertyChanged != null)
                {
                    PropertyChanged(this, new PropertyChangedEventArgs("logtime"));
                }
            }
        }

        public string Username
        {
            get { return _username; }
            set
            {
                _username = value;
                if (PropertyChanged != null)
                {
                    PropertyChanged(this, new PropertyChangedEventArgs("username"));
                }
            }
        }

        public string Ensserver
        {
            get { return _ensip; }
            set
            {
                _ensip = value;
                if (PropertyChanged != null)
                {
                    PropertyChanged(this, new PropertyChangedEventArgs("ensip"));
                }
            }
        }

        public string Content
        {
            get { return _content; }
            set
            {
                _content = value;
                if (PropertyChanged != null)
                {
                    PropertyChanged(this, new PropertyChangedEventArgs("content"));
                }
            }
        }

        

        public EnsloginlogItem(string _logtime,string _username,string _ensserver,string _content)
        {
            Logtime = _logtime;
            Username = _username;
            Content = _content;
            Ensserver = _ensserver;
        }
    }
    public class EnsloginlogSource
    {
        public int Count;

        public EnsloginlogSource()
        {
            UpdateCount();
        }

        public ObservableCollection<EnsloginlogItem> Take(int itemcountInt, int skipInt)
        {
            UpdateCount();
            ObservableCollection<EnsloginlogItem> resultList = DbAdapter.GetInstance().GetEnsloginlog(itemcountInt, skipInt);
            return resultList;
        }

        public void UpdateCount()
        {
            Count = DbAdapter.GetInstance().GetEnsloginlogCount();
        }
    }

    public class CtrItem : INotifyPropertyChanged
    {
        public event PropertyChangedEventHandler PropertyChanged;

        //private int typeid;
        private string _ipaddress;
        private int _ctraddress;
        private string _location;
        private string _status;
        private string _offlinetime;
        private string _firstemailtime;
        private string _secondemailtime;
        private string _ensname;

        public string Ipaddress
        {
            get { return _ipaddress; }
            set
            {
                _ipaddress = value;
                if (PropertyChanged != null)
                {
                    PropertyChanged(this, new PropertyChangedEventArgs("ipaddress"));
                }
            }
        }

        public int Ctraddress
        {
            get { return _ctraddress; }
            set
            {
                _ctraddress = value;
                if (PropertyChanged != null)
                {
                    PropertyChanged(this, new PropertyChangedEventArgs("ctraddress"));
                }
            }
        }

        public string Location
        {
            get { return _location; }
            set
            {
                _location = value;
                if (PropertyChanged != null)
                {
                    PropertyChanged(this, new PropertyChangedEventArgs("location"));
                }
            }
        }

        public string Status
        {
            get { return _status; }
            set
            {
                _status = value;
                if (PropertyChanged != null)
                {
                    PropertyChanged(this, new PropertyChangedEventArgs("status"));
                }
            }
        }

        public string Offlinetime
        {
            get { return _offlinetime; }
            set
            {
                _offlinetime = value;
                if (PropertyChanged != null)
                {
                    PropertyChanged(this, new PropertyChangedEventArgs("offlinetime"));
                }
            }
        }

        public string Secondemailtime
        {
            get { return _secondemailtime; }
            set
            {
                _secondemailtime = value;
                if (PropertyChanged != null)
                {
                    PropertyChanged(this, new PropertyChangedEventArgs("secondemailtime"));
                }
            }
        }

        public string Firstemailtime
        {
            get { return _firstemailtime; }
            set
            {
                _firstemailtime = value;
                if (PropertyChanged != null)
                {
                    PropertyChanged(this, new PropertyChangedEventArgs("firstemailtime"));
                }
            }
        }

        public string Ensname
        {
            get { return _ensname; }
            set
            {
                _ensname = value;
                if (PropertyChanged != null)
                {
                    PropertyChanged(this, new PropertyChangedEventArgs("ensname"));
                }
            }
        }

        public CtrItem(string ipStr, int addressInt, string locationStr,string statusStr,string offlineStr,string ensname)
        {
            Ipaddress = ipStr;
            Ctraddress = addressInt;
            Location = locationStr;
            Status = statusStr;
            Offlinetime = offlineStr;
            Ensname = ensname;
        }


    }

    public class CtrSource
    {
        public int Count;

        public CtrSource()
        {
            UpdateCount();
        }

        public ObservableCollection<CtrItem> Take(int itemcountInt, int skipInt)
        {
            
            ObservableCollection<CtrItem> resultList = DbAdapter.GetInstance().GetCtr(itemcountInt, skipInt);


            return resultList;
        }

        public void UpdateCount()
        {
            Count = DbAdapter.GetInstance().GetCtrCount();
        }
    }

}
