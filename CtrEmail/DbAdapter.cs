using CtrEmail.ViewModel;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data;
using System.Data.SqlClient;
using System.Data.SQLite;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;

namespace CtrEmail
{
    class DbAdapter
    {
        private MainWindow mainWin = null;
        private static DbAdapter _SingletonThird = new DbAdapter();
        private string dbname = "nav.sqlite";

        public static DbAdapter GetInstance()
        {
            if(_SingletonThird.mainWin == null)
            {
                App app = Application.Current as App;
                _SingletonThird.mainWin = app.mainWin;
            }

            // 初始化数据库
            _SingletonThird.InitializeDatabase();

            return _SingletonThird;
        }

        // 初始化数据库，创建必要的表
        private void InitializeDatabase()
        {
            try
            {
                using (SQLiteConnection cn = new SQLiteConnection("data source=" + dbname))
                {
                    cn.Open();
                    using (SQLiteCommand cmd = new SQLiteCommand())
                    {
                        cmd.Connection = cn;

                        // 创建ensserver表
                        cmd.CommandText = @"CREATE TABLE IF NOT EXISTS ensserver (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            ip TEXT NOT NULL,
                            username TEXT,
                            password TEXT,
                            ensname TEXT,
                            emailfrom TEXT,
                            emailto TEXT,
                            emailcontent TEXT,
                            emailtype INTEGER DEFAULT 0,
                            smtpserver TEXT,
                            smtpport INTEGER DEFAULT 587,
                            smtpusername TEXT,
                            smtppassword TEXT,
                            smtpssl INTEGER DEFAULT 1
                        )";
                        cmd.ExecuteNonQuery();

                        // 创建其他必要的表
                        cmd.CommandText = @"CREATE TABLE IF NOT EXISTS syslog (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            typeid INTEGER,
                            username TEXT,
                            content TEXT,
                            logtime DATETIME DEFAULT CURRENT_TIMESTAMP
                        )";
                        cmd.ExecuteNonQuery();

                        cmd.CommandText = @"CREATE TABLE IF NOT EXISTS emaillog (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            ensserver TEXT,
                            emailfrom TEXT,
                            emailto TEXT,
                            emailcontent TEXT,
                            sendtime DATETIME DEFAULT CURRENT_TIMESTAMP,
                            status TEXT
                        )";
                        cmd.ExecuteNonQuery();

                        cmd.CommandText = @"CREATE TABLE IF NOT EXISTS emailensloginlog (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            username TEXT,
                            ensserver TEXT,
                            content TEXT,
                            logtime DATETIME DEFAULT CURRENT_TIMESTAMP
                        )";
                        cmd.ExecuteNonQuery();

                        cmd.CommandText = @"CREATE TABLE IF NOT EXISTS ctroperationlog (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            typeid INTEGER,
                            username TEXT,
                            cardno TEXT,
                            doorid INTEGER,
                            doorlocation TEXT,
                            content TEXT,
                            logtime DATETIME DEFAULT CURRENT_TIMESTAMP
                        )";
                        cmd.ExecuteNonQuery();

                        cmd.CommandText = @"CREATE TABLE IF NOT EXISTS emailcontroller (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            name TEXT,
                            status TEXT
                        )";
                        cmd.ExecuteNonQuery();
                    }
                }

                EnsLog.GetInstance().Write("数据库初始化完成");
            }
            catch (Exception ex)
            {
                EnsLog.GetInstance().Write("数据库初始化失败: " + ex.Message);
            }
        }

        public enum DbLogEnum
        {
            LOGIN=1,
            ADD_DOOR_AUTH=2,
        }
        public bool InsertLogin(ViewModel.EnsItem ensServer)      //用于查询；其实是相当于提供一个可以传参的函数，到时候写一个sql语句，存在string里，传给这个函数，就会自动执行。
        {
            try
            {
                App app = Application.Current as App;
                string usernameStr = ensServer.Username;

                using (SQLiteConnection cn = new SQLiteConnection("data source=" + dbname))
                {
                    if (cn.State != System.Data.ConnectionState.Open)
                    {
                        cn.Open();
                        using (SQLiteCommand cmd = new SQLiteCommand())
                        {
                            cmd.Connection = cn;
                            cmd.CommandText = string.Format("insert into syslog (typeid,username,content) values ({0},'{1}','登录系统')", (int)(DbLogEnum.LOGIN), usernameStr);
                            cmd.ExecuteNonQuery();
                        }   
                    }
                }
            }
            catch (Exception ex)
            {
                EnsLog.GetInstance().Write("InsertLogin:"+ex.Message);
                return false;
            }
            return true;
        }

        public bool InsertLogout(ViewModel.EnsItem ensServer)      //用于查询；其实是相当于提供一个可以传参的函数，到时候写一个sql语句，存在string里，传给这个函数，就会自动执行。
        {
            try
            {
                App app = Application.Current as App;
                string usernameStr = ensServer.Username;
                if (string.IsNullOrEmpty(ensServer.Session))
                    return true;
                using (SQLiteConnection cn = new SQLiteConnection("data source=" + dbname))
                {
                    if (cn.State != System.Data.ConnectionState.Open)
                    {
                        cn.Open();
                        using (SQLiteCommand cmd = new SQLiteCommand())
                        {
                            cmd.Connection = cn;
                            cmd.CommandText = string.Format("insert into syslog (typeid,username,content) values ({0},'{1}','退出登录')", (int)(DbLogEnum.LOGIN), usernameStr);
                            cmd.ExecuteNonQuery();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                EnsLog.GetInstance().Write("InsertLogout:"+ex.Message);
                return false;
            }
            return true;
        }


        public bool InsertCgAuth(ViewModel.EnsItem ensServer, ObservableCollection<CardAuth> cgList)      //用于查询；其实是相当于提供一个可以传参的函数，到时候写一个sql语句，存在string里，传给这个函数，就会自动执行。
        {
            try
            {
                App app = Application.Current as App;
                string usernameStr = ensServer.Username;

                using (SQLiteConnection cn = new SQLiteConnection("data source=" + dbname))
                {
                    if (cn.State != System.Data.ConnectionState.Open)
                    {
                        cn.Open();
                        foreach (CardAuth cardAuth in cgList)
                        {
                            string cardnoStr = cardAuth.Cardno;
                            foreach (Door door in cardAuth.Doors)
                            {
                                using (SQLiteCommand cmd = new SQLiteCommand())
                                {
                                    cmd.Connection = cn;
                                    cmd.CommandText = string.Format("insert into ctroperationlog (typeid,username,cardno,doorid,doorlocation,content) values ({0},'{1}','{2}',{3},'{4}', '增加数据库门权限')", (int)DbLogEnum.ADD_DOOR_AUTH, usernameStr, cardnoStr, door.Doorid, door.Location);    // typeid
                                    cmd.ExecuteNonQuery();
                                }
                            }
                        }
                        cn.Close();
                    }
                }
                    
            }
            catch (Exception ex)
            {
                EnsLog.GetInstance().Write("InsertCgAuth:"+ex.Message);
                return false;
            }
            return true;
        }

        public int GetEnsloginlogCount()
        {

            int countInt = 0;
            try
            {
                using (SQLiteConnection cn = new SQLiteConnection("data source=" + dbname))
                {
                    if (cn.State != System.Data.ConnectionState.Open)
                    {
                        cn.Open();
                        using (SQLiteCommand cmd = new SQLiteCommand())
                        {
                            cmd.Connection = cn;
                            cmd.CommandText = "SELECT COUNT(id) AS count FROM emailensloginlog";

                            using (SQLiteDataReader reader = cmd.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    Int32.TryParse(reader["count"].ToString(), out countInt);
                                }
                            }
                        }
                            
                    }
                }
                    
                return countInt;
            }
            catch (Exception ex)
            {
                EnsLog.GetInstance().Write("GetEnsloginlogCount:" + ex.Message);
                return 0;
            }   
        }

        public ObservableCollection<ViewModel.EnsloginlogItem> GetEnsloginlog(int itemcountInt,int skipInt)
        {
            ObservableCollection<ViewModel.EnsloginlogItem> resultList = new ObservableCollection<CtrEmail.ViewModel.EnsloginlogItem>();
            
            try
            {
                using (SQLiteConnection cn = new SQLiteConnection("data source=" + dbname))
                {
                    if (cn.State != System.Data.ConnectionState.Open)
                    {
                        cn.Open();
                        using (SQLiteCommand cmd = new SQLiteCommand())
                        {
                            cmd.Connection = cn;
                            cmd.CommandText = string.Format("select * from emailensloginlog  order by id limit {1} offset {0}", skipInt, itemcountInt);
                            using (SQLiteDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    string logtime = reader["logtime"].ToString();
                                    logtime = Convert.ToDateTime(logtime).ToString("yyyy-MM-dd HH:mm:ss");
                                    string username = reader["username"].ToString();
                                    string ensserver = reader["ensserver"].ToString();
                                    string content = reader["content"].ToString();
                                    resultList.Add(new EnsloginlogItem(logtime, username, ensserver, content));
                                }
                            }
                        }
                    }
                }
                return resultList;
            }
            catch (Exception ex)
            {
                EnsLog.GetInstance().Write("GetEnsloginlog:"+ex.Message);
            }
            return resultList;
        }

        public int GetCtroperationlogCount()
        {
            int countInt = 0;
            try
            {
                using (SQLiteConnection cn = new SQLiteConnection("data source=" + dbname))
                {
                    if (cn.State != System.Data.ConnectionState.Open)
                    {
                        cn.Open();
                        using (SQLiteCommand cmd = new SQLiteCommand())
                        {
                            cmd.Connection = cn;
                            cmd.CommandText = "SELECT COUNT(id) AS count FROM ctroperationlog";
                            using (SQLiteDataReader reader = cmd.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    Int32.TryParse(reader["count"].ToString(), out countInt);
                                }
                            }
                        }
                    }
                }
                    
                return countInt;
            }
            catch (Exception ex)
            {
                EnsLog.GetInstance().Write("GetCtroperationlogCount:" + ex.Message);
                return 0;
            }
        }

        public ObservableCollection<ViewModel.CtroperationlogItem> GetCtroperationlog(int itemcountInt, int skipInt)
        {
            ObservableCollection<ViewModel.CtroperationlogItem> resultList = new ObservableCollection<CtrEmail.ViewModel.CtroperationlogItem>();
            
            try
            {
                using (SQLiteConnection cn = new SQLiteConnection("data source=" + dbname))
                {
                    if (cn.State != System.Data.ConnectionState.Open)
                    {
                        cn.Open();
                        using (SQLiteCommand cmd = new SQLiteCommand())
                        {
                            cmd.Connection = cn;
                            cmd.CommandText = string.Format("select * from ctroperationlog  order by id limit {1} offset {0}", skipInt, itemcountInt);
                            using (SQLiteDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    string logtime = reader["logtime"].ToString();
                                    logtime = Convert.ToDateTime(logtime).ToString("yyyy-MM-dd HH:mm:ss");
                                    string username = reader["username"].ToString();
                                    string cardno = reader["cardno"].ToString();
                                    string doorlocation = reader["doorlocation"].ToString();
                                    string content = reader["content"].ToString();
                                    resultList.Add(new CtroperationlogItem(logtime, username, cardno, doorlocation, content));
                                }
                            }
                        }
                            
                    }
                }
                return resultList;
            }
            catch (Exception ex)
            {
                EnsLog.GetInstance().Write("GetCtroperationlog:" + ex.Message);
            }

            return resultList;
        }

        public int GetEmaillogCount()
        {
            int countInt = 0;
            try
            {
                using (SQLiteConnection cn = new SQLiteConnection("data source=" + dbname))
                {
                    if (cn.State != System.Data.ConnectionState.Open)
                    {
                        cn.Open();
                        using (SQLiteCommand cmd = new SQLiteCommand())
                        {
                            cmd.Connection = cn;
                            cmd.CommandText = "SELECT COUNT(id) AS count FROM emaillog";
                            using (SQLiteDataReader reader = cmd.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    Int32.TryParse(reader["count"].ToString(), out countInt);
                                }
                            }
                                
                        }
                    }
                }
                return countInt;
            }
            catch (Exception ex)
            {
                EnsLog.GetInstance().Write("GetEmaillogCount:" + ex.Message);
                return 0;
            }
        }

        public ObservableCollection<ViewModel.EmaillogItem> GetEmaillog(int itemcountInt, int skipInt)
        {
            ObservableCollection<ViewModel.EmaillogItem> resultList = new ObservableCollection<CtrEmail.ViewModel.EmaillogItem>();
            
            try
            {
                using (SQLiteConnection cn = new SQLiteConnection("data source=" + dbname))
                {
                    if (cn.State != System.Data.ConnectionState.Open)
                    {
                        cn.Open();
                        using (SQLiteCommand cmd = new SQLiteCommand())
                        {
                            cmd.Connection = cn;
                            cmd.CommandText = string.Format("select * from emaillog order by id limit {1} offset {0}", skipInt, itemcountInt);

                            using (SQLiteDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    string ensname = reader["ensname"].ToString();
                                    string ctrlocation = reader["ctrlocation"].ToString();
                                    string emailfrom = reader["emailfrom"].ToString();
                                    string emailto = reader["emailto"].ToString();
                                    string emailtime = reader["emailtime"].ToString();
                                    emailtime = Convert.ToDateTime(emailtime).ToString("yyyy-MM-dd HH:mm:ss");
                                    string offlinetime = reader["offlinetime"].ToString();
                                    offlinetime = Convert.ToDateTime(offlinetime).ToString("yyyy-MM-dd HH:mm:ss");

                                    resultList.Add(new EmaillogItem(ensname, ctrlocation, emailfrom, emailto, emailtime, offlinetime));
                                }
                            }
                        }
                            
                    }
                }
                return resultList;
            }
            catch (Exception ex)
            {
                EnsLog.GetInstance().Write("GetEmaillog:" + ex.Message);
            }

            return resultList;
        }

        public ObservableCollection<ViewModel.CtrItem> GetCtr(int itemcountInt, int skipInt)
        {
            ObservableCollection<ViewModel.CtrItem> resultList = new ObservableCollection<CtrEmail.ViewModel.CtrItem>();
            
            try
            {

                using (SQLiteConnection cn = new SQLiteConnection("data source=" + dbname))
                {
                    if (cn.State != System.Data.ConnectionState.Open)
                    {
                        cn.Open();
                        using (SQLiteCommand cmd = new SQLiteCommand())
                        {
                            cmd.Connection = cn;
                            cmd.CommandText = string.Format("select * from emailcontroller  order by id limit {1} offset {0}", skipInt, itemcountInt);


                            using (SQLiteDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    string ipStr = reader["ip"].ToString();
                                    if (string.IsNullOrEmpty(ipStr))
                                    {
                                        continue;
                                    }
                                    int addressInt = 0;
                                    string addressStr = reader["address"].ToString();
                                    Int32.TryParse(addressStr, out addressInt);
                                    string locationStr = reader["location"].ToString();
                                    string statusStr = reader["status"].ToString();

                                    string offlineStr = "";
                                    if (!string.IsNullOrEmpty(statusStr) && statusStr.Equals("offline"))
                                    {
                                        offlineStr = reader["offlinetime"].ToString();
                                        offlineStr = Convert.ToDateTime(offlineStr).ToString("yyyy-MM-dd HH:mm:ss");
                                    }
                                    if ("offline".Equals(statusStr))
                                    {
                                        statusStr = "离线";
                                    }
                                    else
                                    {
                                        statusStr = "在线";
                                    }

                                    int ensidInt = 0;
                                    string ensidStr = reader["ensid"].ToString();
                                    Int32.TryParse(ensidStr, out ensidInt);
                                    string ensnameStr = "";

                                    foreach (EnsItem ens in mainWin.ensList)
                                    {
                                        if (ens.Id != 0 && ens.Id == ensidInt)
                                        {
                                            ensnameStr = ens.Ensname;
                                        }
                                    }

                                    resultList.Add(new CtrItem(ipStr, addressInt, locationStr, statusStr, offlineStr, ensnameStr));
                                }
                            }
                                
                        }
                            
                    }
                }
                    
                return resultList;
            }
            catch (Exception ex)
            {
                EnsLog.GetInstance().Write("GetCtr:" + ex.Message);
            }
            return resultList;
        }

        public int GetCtrCount()
        {
            int countInt = 0;
            try
            {
                using (SQLiteConnection cn = new SQLiteConnection("data source=" + dbname))
                {
                    if (cn.State != System.Data.ConnectionState.Open)
                    {
                        cn.Open();
                        using (SQLiteCommand cmd = new SQLiteCommand())
                        {
                            cmd.Connection = cn;
                            cmd.CommandText = "SELECT COUNT(id) AS count FROM emailcontroller";
                            using (SQLiteDataReader reader = cmd.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    Int32.TryParse(reader["count"].ToString(), out countInt);
                                }
                            }
                        }
                    }
                }
                return countInt;
            }
            catch (Exception ex)
            {
                EnsLog.GetInstance().Write("GetCtrCount:" + ex.Message);
                return 0;
            }
        }

        public int GetEnsCount()
        {
            int countInt = 0;
            try
            {
                using (SQLiteConnection cn = new SQLiteConnection("data source=" + dbname))
                {
                    if (cn.State != System.Data.ConnectionState.Open)
                    {
                        cn.Open();
                        using (SQLiteCommand cmd = new SQLiteCommand())
                        {
                            cmd.Connection = cn;
                            cmd.CommandText = "SELECT COUNT(id) AS count FROM ensserver";
                            using (SQLiteDataReader reader = cmd.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    Int32.TryParse(reader["count"].ToString(), out countInt);
                                }
                            }
                                
                        }
                            
                    }
                }
                    
                return countInt;
            }
            catch (Exception ex)
            {
                EnsLog.GetInstance().Write("GetEnsCount:" + ex.Message);
                return 0;
            }
        }

        public ObservableCollection<ViewModel.EnsItem> GetEns(int itemcountInt, int skipInt)
        {
            ObservableCollection<ViewModel.EnsItem> resultList = new ObservableCollection<CtrEmail.ViewModel.EnsItem>();
            
            try
            {
                using (SQLiteConnection cn = new SQLiteConnection("data source=" + dbname))
                {
                    if (cn.State != System.Data.ConnectionState.Open)
                    {
                        cn.Open();
                        using (SQLiteCommand cmd = new SQLiteCommand())
                        {
                            cmd.Connection = cn;
                            cmd.CommandText = string.Format("select * from ensserver  order by id limit {1} offset {0}", skipInt, itemcountInt);
                            using (SQLiteDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    string idStr = reader["id"].ToString();
                                    int idInt = Convert.ToInt32(idStr);
                                    string ip = reader["ip"].ToString();
                                    string username = Utils.Util.decrypt(reader["username"].ToString());
                                    string password = Utils.Util.decrypt(reader["password"].ToString());
                                    string ensname = reader["ensname"].ToString();
                                    string emailfrom = reader["emailfrom"].ToString();
                                    string emailto = reader["emailto"].ToString();
                                    string emailcontent = reader["emailcontent"].ToString();

                                    // 读取SMTP相关字段
                                    int emailtype = 0;
                                    string smtpserver = "";
                                    int smtpport = 587;
                                    string smtpusername = "";
                                    string smtppassword = "";
                                    bool smtpssl = true;

                                    try
                                    {
                                        if (reader["emailtype"] != DBNull.Value)
                                            emailtype = Convert.ToInt32(reader["emailtype"]);
                                        if (reader["smtpserver"] != DBNull.Value)
                                            smtpserver = reader["smtpserver"].ToString();
                                        if (reader["smtpport"] != DBNull.Value)
                                            smtpport = Convert.ToInt32(reader["smtpport"]);
                                        if (reader["smtpusername"] != DBNull.Value)
                                            smtpusername = Utils.Util.decrypt(reader["smtpusername"].ToString());
                                        if (reader["smtppassword"] != DBNull.Value)
                                            smtppassword = Utils.Util.decrypt(reader["smtppassword"].ToString());
                                        if (reader["smtpssl"] != DBNull.Value)
                                            smtpssl = Convert.ToBoolean(reader["smtpssl"]);
                                    }
                                    catch
                                    {
                                        // 如果字段不存在，使用默认值
                                    }

                                    resultList.Add(new EnsItem(idInt, ip, username, password, ensname, emailfrom, emailto, emailcontent,
                                                              emailtype, smtpserver, smtpport, smtpusername, smtppassword, smtpssl));
                                }
                            }
                                
                        }
                            
                    }
                }
                    
                return resultList;
            }
            catch (Exception ex)
            {
                EnsLog.GetInstance().Write("GetEns:" + ex.Message);
            }
            return resultList;
        }

        public int AddEns(EnsItem ensItem)
        {
            try
            {
                string sqlStr = "";
                using (SQLiteConnection cn = new SQLiteConnection("data source=" + dbname))
                {
                    if (cn.State != System.Data.ConnectionState.Open)
                    {
                        cn.Open();
                        int idInt = 0;
                        using (SQLiteCommand cmd = new SQLiteCommand())
                        {
                            cmd.Connection = cn;
                            cmd.CommandText = string.Format("insert into ensserver (ip,username,password,ensname,emailfrom,emailto,emailcontent,emailtype,smtpserver,smtpport,smtpusername,smtppassword,smtpssl) values ('{0}','{1}','{2}','{3}','{4}','{5}','{6}',{7},'{8}',{9},'{10}','{11}',{12})",
                                ensItem.Ip, Utils.Util.encrypt(ensItem.Username), Utils.Util.encrypt(ensItem.Password), ensItem.Ensname, ensItem.Emailfrom, ensItem.Emailto, ensItem.Emailcontent,
                                ensItem.Emailtype, ensItem.Smtpserver ?? "", ensItem.Smtpport,
                                Utils.Util.encrypt(ensItem.Smtpusername ?? ""), Utils.Util.encrypt(ensItem.Smtppassword ?? ""), ensItem.Smtpssl ? 1 : 0);
                            sqlStr = cmd.CommandText;
                            EnsLog.GetInstance().Write("addens sql:" + sqlStr);

                            int resultInt = cmd.ExecuteNonQuery();

                            cmd.CommandText = string.Format("select id from ensserver where ip='{0}'", ensItem.Ip);
                            using (SQLiteDataReader dr = cmd.ExecuteReader())
                            {
                                
                                if (dr.Read())
                                {
                                    Int32.TryParse(dr["id"].ToString(), out idInt);
                                }
                            }
                        }
                        return idInt;
                    }
                }
                    
            }
            catch (Exception ex)
            {
                EnsLog.GetInstance().Write("AddEns:" + ex.Message);
            }
            return 0;
        }

        public int ModEns(EnsItem ensItem)
        {
            try
            {
                using (SQLiteConnection cn = new SQLiteConnection("data source=" + dbname))
                {
                    if (cn.State != System.Data.ConnectionState.Open)
                    {
                        cn.Open();
                        int resultInt = 0;
                        using (SQLiteCommand cmd = new SQLiteCommand())
                        {
                            cmd.Connection = cn;
                            cmd.CommandText = string.Format("update ensserver set username='{0}',password='{1}',ensname='{2}',emailfrom='{3}',emailto='{4}',emailcontent='{5}',emailtype={6},smtpserver='{7}',smtpport={8},smtpusername='{9}',smtppassword='{10}',smtpssl={11} where ip='{12}';",
                                 Utils.Util.encrypt(ensItem.Username), Utils.Util.encrypt(ensItem.Password), ensItem.Ensname, ensItem.Emailfrom, ensItem.Emailto, ensItem.Emailcontent,
                                 ensItem.Emailtype, ensItem.Smtpserver ?? "", ensItem.Smtpport,
                                 Utils.Util.encrypt(ensItem.Smtpusername ?? ""), Utils.Util.encrypt(ensItem.Smtppassword ?? ""), ensItem.Smtpssl ? 1 : 0, ensItem.Ip);
                            resultInt = cmd.ExecuteNonQuery();
                        }
                            
                        return resultInt;
                    }
                }
                    
            }
            catch (Exception ex)
            {
                EnsLog.GetInstance().Write("GetEnsloginlogCount:" + ex.Message);
            }
            return 0;
        }

        public bool DelEns(EnsItem ensItem)
        {
            
            try
            {
                using (SQLiteConnection cn = new SQLiteConnection("data source=" + dbname))
                {
                    if (cn.State != System.Data.ConnectionState.Open)
                    {
                        cn.Open();
                        using (SQLiteCommand cmd = new SQLiteCommand())
                        {
                            cmd.Connection = cn;
                            cmd.CommandText = string.Format("delete from ensserver where ip='{0}'", ensItem.Ip);

                            int resultInt = cmd.ExecuteNonQuery();
                            cn.Close();
                            if (resultInt > 0)
                                return true;
                            else
                                return false;
                        }
                            
                    }
                }
                    
            }
            catch (Exception ex)
            {
                EnsLog.GetInstance().Write("DelEns:" + ex.Message);
            }
            return false;
        }

    }
}
