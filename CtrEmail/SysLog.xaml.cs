using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CtrEmail
{
    /// <summary>
    /// Ensloginlog.xaml 的交互逻辑
    /// </summary>
    public partial class SysLog : UserControl
    {
        public SysLog()
        {
            InitializeComponent();

            init();

        }
        MainWindow mainWin;
        private void init()
        {
            App app = Application.Current as App;
            mainWin = app.mainWin;

            DataContext = new EnsloginlogModel();
            mainWin.syslogDatagrid = syslogDatagrid;

        }
    }

    public class EnsloginlogModel : BaseViewModel
    {
        private ObservableCollection<ViewModel.EnsloginlogItem> _datagridSoruce;

        public  ObservableCollection<ViewModel.EnsloginlogItem> DatagridSource
        {
            get
            {
                return _datagridSoruce;
            }
            set
            {
                if (_datagridSoruce != value)
                {
                    _datagridSoruce = value;
                    OnPropertyChanged("DatagridSource");
                }
            }
        }

        //private List<EnsloginlogItem> _source;
        private ViewModel.EnsloginlogSource _source = new ViewModel.EnsloginlogSource();
        MainWindow mainWin;
        public EnsloginlogModel()
        {
            _currentPage = 1;

            App app = Application.Current as App;
            mainWin = app.mainWin;

            _totalPage = (int)Math.Ceiling(_source.Count*1.0 / mainWin.pagesizeInt);

            _datagridSoruce = new ObservableCollection<ViewModel.EnsloginlogItem>();

            List<ViewModel.EnsloginlogItem> result = _source.Take(mainWin.pagesizeInt, 0).ToList();

            _datagridSoruce.Clear();

            _datagridSoruce.AddRange(result);

            _firstPageCommand = new DelegateCommand(FirstPageAction);

            _previousPageCommand = new DelegateCommand(PreviousPageAction);

            _nextPageCommand = new DelegateCommand(NextPageAction);

            _lastPageCommand = new DelegateCommand(LastPageAction);
        }

        private void FirstPageAction()
        {
            CurrentPage = 1;

            var result = _source.Take(mainWin.pagesizeInt, 0).ToList();

            _datagridSoruce.Clear();

            _datagridSoruce.AddRange(result);
        }

        private void PreviousPageAction()
        {
            if (CurrentPage == 1)
            {
                return;
            }

            List<ViewModel.EnsloginlogItem> result = new List<ViewModel.EnsloginlogItem>();

            int skipnumInt = (CurrentPage - 2) * mainWin.pagesizeInt;
            if (skipnumInt < 0)
                skipnumInt = 0;

            result = _source.Take(mainWin.pagesizeInt, skipnumInt).ToList();

            _datagridSoruce.Clear();

            _datagridSoruce.AddRange(result);

            CurrentPage--;
        }

        private void NextPageAction()
        {
            if (CurrentPage == _totalPage)
            {
                return;
            }

            List<ViewModel.EnsloginlogItem> result = new List<ViewModel.EnsloginlogItem>();

            result = _source.Take(mainWin.pagesizeInt, CurrentPage * mainWin.pagesizeInt).ToList();

            _datagridSoruce.Clear();

            _datagridSoruce.AddRange(result);

            CurrentPage++;
        }

        private void LastPageAction()
        {
            CurrentPage = TotalPage;

            int skipCount = (_totalPage - 1) * mainWin.pagesizeInt;
            int takeCount = _source.Count - skipCount;

            var result = _source.Take(takeCount, skipCount).ToList();

            _datagridSoruce.Clear();

            _datagridSoruce.AddRange(result);
        }
    }
}
