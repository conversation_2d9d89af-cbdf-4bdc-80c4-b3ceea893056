using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CtrEmail.ViewModel
{
    public class EnsItem : INotifyPropertyChanged
    {
        public event PropertyChangedEventHandler PropertyChanged;
        private int _id;
        private string _ip;
        private string _username;
        private string _password;
        private string _ensname;
        private string _emailfrom;
        private string _emailto;
        private string _emailcontent;
        private string _session;

        // SMTP相关属性
        private int _emailtype = 0; // 0: API方式, 1: SMTP方式
        private string _smtpserver;
        private int _smtpport = 587;
        private string _smtpusername;
        private string _smtppassword;
        private bool _smtpssl = true;

        private string _name;


        public int Id
        {
            get { return _id; }
            set { _id = value; }
        }

        public string Ip
        {
            get { return _ip; }
            set
            {
                _ip = value;
                if (PropertyChanged != null)
                {
                    PropertyChanged(this, new PropertyChangedEventArgs("ip"));
                }
                if (string.IsNullOrEmpty(_ensname))
                {
                    _ensname = value;
                }
            }
        }

        public string Username
        {
            get { return _username; }
            set
            {
                _username = value;
                if (PropertyChanged != null)
                {
                    PropertyChanged(this, new PropertyChangedEventArgs("username"));
                }
            }
        }

        public string Password
        {
            get { return _password; }
            set
            {
                _password = value;
                if (PropertyChanged != null)
                {
                    PropertyChanged(this, new PropertyChangedEventArgs("password"));
                }
            }
        }

        public string Ensname
        {
            get { return _ensname; }
            set
            {
                _ensname = value;
                if (PropertyChanged != null)
                {
                    PropertyChanged(this, new PropertyChangedEventArgs("ensname"));
                }
            }
        }

        public string Emailfrom
        {
            get { return _emailfrom; }
            set
            {
                _emailfrom = value;
                if (PropertyChanged != null)
                {
                    PropertyChanged(this, new PropertyChangedEventArgs("emailfrom"));
                }
            }
        }

        public string Emailto
        {
            get { return _emailto; }
            set
            {
                _emailto = value;
                if (PropertyChanged != null)
                {
                    PropertyChanged(this, new PropertyChangedEventArgs("emailto"));
                }
            }
        }

        public string Emailcontent
        {
            get { return _emailcontent; }
            set
            {
                _emailcontent = value;
                if (PropertyChanged != null)
                {
                    PropertyChanged(this, new PropertyChangedEventArgs("_emailcontent"));
                }
            }
        }

        public string Session
        {
            get { return _session; }
            set
            {
                _session = value;
                if (PropertyChanged != null)
                {
                    //PropertyChanged(this, new PropertyChangedEventArgs("session"));
                }
            }
        }

        // SMTP相关属性
        public int Emailtype
        {
            get { return _emailtype; }
            set
            {
                _emailtype = value;
                if (PropertyChanged != null)
                {
                    PropertyChanged(this, new PropertyChangedEventArgs("Emailtype"));
                    PropertyChanged(this, new PropertyChangedEventArgs("EmailtypeDisplay"));
                }
            }
        }

        public string Smtpserver
        {
            get { return _smtpserver; }
            set
            {
                _smtpserver = value;
                if (PropertyChanged != null)
                {
                    PropertyChanged(this, new PropertyChangedEventArgs("Smtpserver"));
                }
            }
        }

        public int Smtpport
        {
            get { return _smtpport; }
            set
            {
                _smtpport = value;
                if (PropertyChanged != null)
                {
                    PropertyChanged(this, new PropertyChangedEventArgs("Smtpport"));
                }
            }
        }

        public string Smtpusername
        {
            get { return _smtpusername; }
            set
            {
                _smtpusername = value;
                if (PropertyChanged != null)
                {
                    PropertyChanged(this, new PropertyChangedEventArgs("Smtpusername"));
                }
            }
        }

        public string Smtppassword
        {
            get { return _smtppassword; }
            set
            {
                _smtppassword = value;
                if (PropertyChanged != null)
                {
                    PropertyChanged(this, new PropertyChangedEventArgs("Smtppassword"));
                }
            }
        }

        public bool Smtpssl
        {
            get { return _smtpssl; }
            set
            {
                _smtpssl = value;
                if (PropertyChanged != null)
                {
                    PropertyChanged(this, new PropertyChangedEventArgs("Smtpssl"));
                }
            }
        }

        // 用于显示的邮件类型
        public string EmailtypeDisplay
        {
            get { return _emailtype == 1 ? "SMTP" : "API"; }
        }

        public ObservableCollection<Controller> controllerList = new ObservableCollection<Controller>();

        public EnsItem(int id,string ip, string username,string password, string ensname,string emailfrom,string emailto,string emailcontent, string session="")
        {
            Id = id;
            Ip = ip;
            Username = username;
            Password = password;
            Ensname = ensname;
            Emailfrom = emailfrom;
            Emailto = emailto;
            Emailcontent = emailcontent;

            Session = session;

            // 设置默认SMTP配置
            Emailtype = 0; // 默认使用API方式
            Smtpport = 587;
            Smtpssl = true;
        }

        // 新的构造函数，支持SMTP参数
        public EnsItem(int id, string ip, string username, string password, string ensname, string emailfrom, string emailto, string emailcontent,
                      int emailtype, string smtpserver, int smtpport, string smtpusername, string smtppassword, bool smtpssl, string session = "")
        {
            Id = id;
            Ip = ip;
            Username = username;
            Password = password;
            Ensname = ensname;
            Emailfrom = emailfrom;
            Emailto = emailto;
            Emailcontent = emailcontent;
            Emailtype = emailtype;
            Smtpserver = smtpserver;
            Smtpport = smtpport;
            Smtpusername = smtpusername;
            Smtppassword = smtppassword;
            Smtpssl = smtpssl;
            Session = session;
        }

        public EnsItem()
        {
            
        }


    }
    public class EnsSource
    {
        public int Count;

        public EnsSource()
        {

        }

        public ObservableCollection<EnsItem> Take(int itemcountInt, int skipInt)
        {
            ObservableCollection<EnsItem> resultList = DbAdapter.GetInstance().GetEns(itemcountInt, skipInt);


            return resultList;
        }

        public void UpdateCount()
        {
            Count = DbAdapter.GetInstance().GetEnsCount();
        }

        public int Add(EnsItem ensItem)
        {
            return DbAdapter.GetInstance().AddEns(ensItem);
        }

        public int Mod(EnsItem ensItem)
        {
            return DbAdapter.GetInstance().ModEns(ensItem);
        }

        public bool Del(EnsItem ensItem)
        {
            return DbAdapter.GetInstance().DelEns(ensItem);
        }
    }
}
