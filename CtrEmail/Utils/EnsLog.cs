using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.IO;

namespace CtrEmail
{
    public class EnsLog
    {
        private static EnsLog _SingletonThird = new EnsLog();

        public static EnsLog GetInstance()
        {

            return _SingletonThird;
        }

        public void Write(string info,Exception ex=null)
        {
            try
            {
                FileStream fs = new FileStream(@"c:\cardemail_1.txt", FileMode.OpenOrCreate, FileAccess.Write);
                StreamWriter sw = new StreamWriter(fs);
                sw.BaseStream.Seek(0, SeekOrigin.End);
                //sw.WriteLine("WindowsService: Service Started" + DateTime.Now.ToString() + "\n");
                string exStr = "";
                if (ex != null)
                {
                    string stackStr = ex.StackTrace;
                    exStr = " --ex:" + stackStr.Substring(stackStr.LastIndexOf("\\") + 1, stackStr.Length - stackStr.LastIndexOf("\\") - 1);
                }
                sw.WriteLine(DateTime.Now.ToString() + " : " + info + exStr);

                sw.Flush();
                sw.Close();
                fs.Close();
            }
            catch(Exception exc)
            {
                string stackStr = exc.StackTrace;
                string exStr = " --ex:" + stackStr.Substring(stackStr.LastIndexOf("\\") + 1, stackStr.Length - stackStr.LastIndexOf("\\") - 1);
                Console.WriteLine(exStr);
            }
            
        }

    }
}
