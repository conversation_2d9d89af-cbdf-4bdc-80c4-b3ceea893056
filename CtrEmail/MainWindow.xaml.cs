using CtrEmail.ViewModel;
using MaterialDesignThemes.Wpf;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CtrEmail
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        public ListView lastMenuListView = null;   // ���ѡ�е���Ŀ

        public MainWindow()
        {
            InitializeComponent();
            App app = Application.Current as App;
            app.mainWin = this;
            test();
            init();

            InitMenu();
        }
        
        public int pagesizeInt = 15;    // Ĭ����ʾ15��
        public ObservableCollection<ViewModel.EnsItem> ensList;
        
        void init()
        {
            string pagesizeStr = Utils.XmlConfig.getValue("pagesize");
            Int32.TryParse(pagesizeStr, out pagesizeInt);

            ensList = DbAdapter.GetInstance().GetEns(100,0);
        }

        void test()
        {
            // ���Դ���
        }

        private void bgLogin(object sender, DoWorkEventArgs e)
        {
            try
            {
                int enscountInt = DbAdapter.GetInstance().GetEnsCount();
                if (enscountInt <= 0)
                {
                    return;
                }

                for (int i = 0; i < ensList.Count; i++)
                {
                    if (string.IsNullOrEmpty(ensList[i].Ip) || string.IsNullOrEmpty(ensList[i].Username) || string.IsNullOrEmpty(ensList[i].Password))
                    {
                        continue;
                    }
                    string sessionStr = Webservice.login(ensList[i]);
                    if (!string.IsNullOrEmpty(sessionStr))
                    {
                        ensList[i].Session = sessionStr;
                        Webservice.GetControllers(ensList[i], ref ensList[i].controllerList);
                        Webservice.GetStatus(ensList[i], ref ensList[i].controllerList);
                    }
                }
                e.Result = ensList; // ���ؽ��
            }
            catch (Exception)
            {
            }
        }

        private void bgLoginCallback(object sender, RunWorkerCompletedEventArgs e)
        {
            // �ص�����
        }

        internal void SwitchScreen(object sender)
        {
            var screen = ((UserControl)sender);

            if (screen != null)
            {
                StackPanelMain.Children.Clear();
                StackPanelMain.Children.Add(screen);
            }
        }

        void InitMenu()
        {
            var ctrList = new List<SubItem>();
            ctrList.Add(new SubItem("������", new CtrList()));
            ItemMenu ctrItem = new ItemMenu("�豸����", ctrList, PackIconKind.Devices);
            Menu.Children.Add(new UserControlMenuItem(ctrItem, this));

            var logList = new List<SubItem>();
            logList.Add(new SubItem("��¼��־", new SysLog()));
            logList.Add(new SubItem("�ʼ���־", new EmailLog()));
            ItemMenu logItem = new ItemMenu("��־", logList, PackIconKind.AccountBoxMultiple);
            Menu.Children.Add(new UserControlMenuItem(logItem, this));

            var settingList = new List<SubItem>();
            settingList.Add(new SubItem("ENS2000����������",new SettingEns()));
            settingList.Add(new SubItem("ϵͳ��������",new SettingPara()));
            settingList.Add(new SubItem("SMTP�ʼ�����",new SettingEmail())); // ����SMTP�ʼ����ò˵�
            ItemMenu settingItem = new ItemMenu("����", settingList, PackIconKind.Settings);
            Menu.Children.Add(new UserControlMenuItem(settingItem, this));

            if(string.IsNullOrEmpty(Utils.XmlConfig.getValue("dbIp")))   // û�����ݿ���Ϣ
            {
                SwitchScreen(settingList[0].Screen);
            }
            else if (ensList.Count==0)   // û��ens��������Ϣ
            {
                SwitchScreen(settingList[1].Screen);
            }
            else
            {
                SwitchScreen(ctrList[0].Screen);    // Ĭ�ϳ������
            }
        }

        public ListView ctrcardListview = null;
        public ListView dbcardListview = null;
        public ListView cmpcardListview = null;
        public DataGrid ctrDatagrid = null;
        public DataGrid syslogDatagrid = null;
        public DataGrid emailDatagrid = null;
        public DataGrid ensDatagrid = null;
        
        private void OnSizechange(object sender, SizeChangedEventArgs e)
        {
            double listviewHeightDouble = this.ActualHeight - 240;
            if (listviewHeightDouble < 100)
                return;
            double gridHeightDouble = listviewHeightDouble + 150;
            if(ctrcardListview != null)
            {
                ctrcardListview.Height = listviewHeightDouble;
            }
            if(dbcardListview != null)
            {
                dbcardListview.Height = listviewHeightDouble;
            }
            if (cmpcardListview != null)
            {
                cmpcardListview.Height = listviewHeightDouble;
            }

            if (ctrDatagrid != null)
            {
                ctrDatagrid.Height = gridHeightDouble;
            }

            if(syslogDatagrid != null)
            {
                syslogDatagrid.Height = gridHeightDouble;
            }

            if(emailDatagrid != null)
            {
                emailDatagrid.Height = gridHeightDouble;
            }
            if(ensDatagrid != null)
            {
                ensDatagrid.Height = gridHeightDouble;
            }
        }

        private void MainWinClosed(object sender, EventArgs e)
        {
            foreach(ViewModel.EnsItem ensServer in ensList)
            {
                DbAdapter.GetInstance().InsertLogout(ensServer); // �����˳���¼��Ϣ
            }
        }
    }
}
