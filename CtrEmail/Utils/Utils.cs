using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows.Media;
using System.Windows;
using System.Security.Cryptography;
using System.Configuration;
using System.Xml;
using System.IO;
using System.Text.RegularExpressions;
//using System.ServiceModel;
//using System.ServiceModel.Configuration;
using System.Windows.Controls;
using System.Net;
using System.Collections.ObjectModel;
using System.Xml.Serialization;
using System.Runtime.Serialization.Formatters.Binary;

namespace CtrEmail.Utils
{
    public class Util
    {

        public static string md5(string str)
        {
            try
            {
                MD5CryptoServiceProvider md5 = new MD5CryptoServiceProvider();
                byte[] bytValue, bytHash;
                bytValue = System.Text.Encoding.UTF8.GetBytes(str);
                bytHash = md5.ComputeHash(bytValue);
                md5.Clear();
                string sTemp = "";
                for (int i = 0; i < bytHash.Length; i++)
                {
                    sTemp += bytHash[i].ToString("X").PadLeft(2, '0');
                }
                str = sTemp.ToLower();
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
            }

            return str;
        }

        public static IPAddress[] getLocalIp()
        {
            IPAddress[] ips = Dns.GetHostAddresses(Dns.GetHostName()).Where(ip => ip.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork).ToArray();
            return ips;
        }

        public static string EncodeBase64(string source)
        {
            Encoding encode = Encoding.UTF8;
            string enstring = "";
            byte[] bytes = encode.GetBytes(source);
            try
            {
                enstring = Convert.ToBase64String(bytes);
            }
            catch
            {
                enstring = source;
            }
            return enstring;
        }

        public static string DecodeBase64(string result)
        {
            Encoding encode = Encoding.UTF8;
            string decode = "";
            byte[] bytes = Convert.FromBase64String(result);
            try
            {
                decode = encode.GetString(bytes);
            }
            catch
            {
                decode = result;
            }
            return decode;
        }

        public static int SecondsDiff(String starttimeStr, String endtimeStr)
        {
            try
            {
                DateTime startTime = Convert.ToDateTime(starttimeStr);
                DateTime endTime = Convert.ToDateTime(endtimeStr);
                TimeSpan startSpan = new TimeSpan(startTime.Ticks);
                TimeSpan endSpan = new TimeSpan(endTime.Ticks);
                TimeSpan diffSpan = startSpan.Subtract(endSpan).Duration();
                int diffInt = Convert.ToInt32(diffSpan.TotalSeconds);
                return diffInt;
            }
            catch (Exception)
            {
                return 0;
            }
        }


        //isDigit是否是数字
        public static bool isNumberic(string _string)
        {
            if (string.IsNullOrEmpty(_string))
                return false;
            foreach (char c in _string)
            {
                if (!char.IsDigit(c))
                    //if(c<'0' c="">'9')//最好的方法,在下面测试数据中再加一个0，然后这种方法效率会搞10毫秒左右
                    return false;
            }
            return true;
        }

        public static void SortCardauth(ref ObservableCollection<CardAuth> cardauthList)
        {
            int count = cardauthList.Count;
            for(int i = 0; i < count-1; i++)
            {
                for(int j = 0; j < count - 1 - i; j++)
                {
                    if (string.Compare(cardauthList[j].Cardno,cardauthList[j + 1].Cardno)>0)
                    {
                        CardAuth cardAuth = cardauthList[j];
                        cardauthList[j] = cardauthList[j + 1];
                        cardauthList[j + 1] = cardAuth;
                    }
                }
            }
            for(int i = 0; i < count; i++)
            {
                cardauthList[i].SortDoors();
            }
        }

        const string KEY_VAL = "EFC";
        // 使用KEY_VAL加用户设置密码
        public static string encrypt(string val, string key = "ENS2000")
        {
            try
            {
                if (val == null || key == null)
                {
                    return "";
                }
                val = KEY_VAL + val;
                const int MIX_LEN = 32;
                string mixStr = MD5(GetRandomString(MIX_LEN, true, true, true, false));// MD5(key);//GetRandomString(MIX_LEN,true,true,true,false);
                char[] mixArr = mixStr.ToCharArray();
                StringBuilder sb = new StringBuilder();
                int strLen = val.Length;
                char[] valArr = val.ToCharArray();

                for (int i = 0, j = 0; i < strLen; i++, j++)
                {
                    j = j == MIX_LEN ? 0 : j;
                    sb.Append(mixArr[j]);
                    sb.Append((char)(valArr[i] ^ mixArr[j]));
                }
                string tmp = sb.ToString();
                string resultStr = EncodeBase64(bindKey(tmp, key));
                return resultStr.Replace('/', '_').Replace('+','-');
            }
            catch (Exception)
            {
                return "";
            }

        }

        /**
         * @desc解密
         * @param string str 待解密字符串
         * @param string key 密钥
         * @return string
         * 
         *  php 在index.php decrypt 中
         */
        public static string decrypt(string _originalStr, string key = "ENS2000")
        {
            try
            {
                if (string.IsNullOrEmpty(_originalStr))
                    return "";
                string originalStr = _originalStr.Replace('_', '/').Replace('-','+');
                string fullStr = originalStr;
                if(originalStr.Length%4 != 0) // 需要补充尾巴
                {
                    fullStr = (originalStr + "====").Substring(0, (int)(Math.Ceiling((double)originalStr.Length / 4)) * 4);
                }

                string str = bindKey(DecodeBase64(fullStr), key);
                char[] cryptArr = str.ToCharArray();
                int strLen = cryptArr.Length;
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < strLen; i++)
                {
                    sb.Append((char)(cryptArr[i] ^ cryptArr[++i]));
                }
                string resultStr = sb.ToString();
                if (resultStr.Length >= KEY_VAL.Length)
                {
                    string efcStr = resultStr.Substring(0, KEY_VAL.Length);
                    if (efcStr.Equals(KEY_VAL))
                    {
                        return resultStr.Substring(KEY_VAL.Length);
                    }

                }
            }
            catch (Exception)
            {
                return _originalStr;
            }
            return _originalStr;
        }

        public static string GetRandomString(int length, bool useNum, bool useLow, bool useUpp, bool useSpe = false, string custom = "")
        {
            byte[] b = new byte[4];
            new System.Security.Cryptography.RNGCryptoServiceProvider().GetBytes(b);
            Random r = new Random(BitConverter.ToInt32(b, 0));
            string s = null, str = custom;
            if (useNum == true) { str += "**********"; }
            if (useLow == true) { str += "abcdefghijklmnopqrstuvwxyz"; }
            if (useUpp == true) { str += "ABCDEFGHIJKLMNOPQRSTUVWXYZ"; }
            if (useSpe == true) { str += "!\"#$%&'()*+,-./:;<=>?@[\\]^_`{|}~"; }
            for (int i = 0; i < length; i++)
            {
                s += str.Substring(r.Next(0, str.Length - 1), 1);
            }
            return s;
        }

        public static string MD5(string password)
        {
            byte[] textBytes = System.Text.Encoding.Default.GetBytes(password);
            try
            {
                System.Security.Cryptography.MD5CryptoServiceProvider cryptHandler;
                cryptHandler = new System.Security.Cryptography.MD5CryptoServiceProvider();
                byte[] hash = cryptHandler.ComputeHash(textBytes);
                string ret = "";
                foreach (byte a in hash)
                {
                    if (a < 16)
                        ret += "0" + a.ToString("x");
                    else
                        ret += a.ToString("x");
                }
                return ret;
            }
            catch
            {
                throw;
            }

        }

        /**
	     * @desc辅助方法 用密钥对随机化操作后的字符串进行处理
	     * @param str
	     * @param key
	     * @return string
	     */
        public static string bindKey(string str, string key)
        {

            string encrypt_key = MD5(key);
            char[] encryptArr = encrypt_key.ToCharArray();
            StringBuilder sb = new StringBuilder();
            char[] valArr = str.ToCharArray();
            int strLen = valArr.Length;
            for (int i = 0, j = 0; i < strLen; i++, j++)
            {
                j = j == 32 ? 0 : j;
                char ch = (char)(valArr[i] ^ encryptArr[j]);
                sb.Append(ch);
            }
            return sb.ToString();
        }

        

    }

    //  http://192.168.1.157/nav/system/employee/soapserver?wsdl


    /// <summary>
    /// 对象的深度拷贝（序列化的方式）
    /// </summary>
    public static class DeepCopy
    {

        /// <summary>
        /// xml序列化的方式实现深拷贝
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="t"></param>
        /// <returns></returns>
        public static T XmlDeepCopy<T>(T t)
        {
            //创建Xml序列化对象
            XmlSerializer xml = new XmlSerializer(typeof(T));
            using (MemoryStream ms = new MemoryStream())//创建内存流
            {
                //将对象序列化到内存中
                xml.Serialize(ms, t);
                ms.Position = 0;//将内存流的位置设为0
                return (T)xml.Deserialize(ms);//继续反序列化
            }
        }

        /// <summary>
        /// 二进制序列化的方式进行深拷贝
        /// 确保需要拷贝的类里的所有成员已经标记为 [Serializable] 如果没有加该特性特报错
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="t"></param>
        /// <returns></returns>
        public static T BinaryDeepCopy<T>(T t)
        {
            //创建二进制序列化对象
            BinaryFormatter bf = new BinaryFormatter();
            using (MemoryStream ms = new MemoryStream())//创建内存流
            {
                //将对象序列化到内存中
                bf.Serialize(ms, t);
                ms.Position = 0;//将内存流的位置设为0
                return (T)bf.Deserialize(ms);//继续反序列化
            }
        }
    }
}
