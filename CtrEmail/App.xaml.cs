using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;

namespace CtrEmail
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        public MainWindow mainWin = null;

        System.Threading.Mutex mutex;

        public App()
        {
            this.Startup += new StartupEventHandler(App_Startup);
        }

        void App_Startup(object sender, StartupEventArgs e)
        {
            /*bool ret;
            mutex = new System.Threading.Mutex(true, "CtrEmail", out ret);

            if (!ret)
            {
                MessageBox.Show("程序已打开", "", MessageBoxButton.OK, MessageBoxImage.Stop);
                Environment.Exit(0);
            }*/
        }
    }
}
