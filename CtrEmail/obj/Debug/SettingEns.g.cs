#pragma checksum "..\..\SettingEns.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "824DD54B9D91A7C96F5091BE898D99EEB2648021B0F89D9617B411FD0690A59D"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using CtrEmail;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Interactivity;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace CtrEmail {
    
    
    /// <summary>
    /// SettingEns
    /// </summary>
    public partial class SettingEns : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 11 "..\..\SettingEns.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel listStack;
        
        #line default
        #line hidden
        
        
        #line 18 "..\..\SettingEns.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid datagrid;
        
        #line default
        #line hidden
        
        
        #line 67 "..\..\SettingEns.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel textStack;
        
        #line default
        #line hidden
        
        
        #line 105 "..\..\SettingEns.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox nameBox;
        
        #line default
        #line hidden
        
        
        #line 108 "..\..\SettingEns.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ipBox;
        
        #line default
        #line hidden
        
        
        #line 111 "..\..\SettingEns.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox usernameBox;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\SettingEns.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.PasswordBox passwordBox;
        
        #line default
        #line hidden
        
        
        #line 117 "..\..\SettingEns.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox sendBox;
        
        #line default
        #line hidden
        
        
        #line 124 "..\..\SettingEns.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RichTextBox receiveBox;
        
        #line default
        #line hidden
        
        
        #line 129 "..\..\SettingEns.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton apiRadio;
        
        #line default
        #line hidden
        
        
        #line 130 "..\..\SettingEns.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton smtpRadio;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\SettingEns.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock lblApiServer;
        
        #line default
        #line hidden
        
        
        #line 135 "..\..\SettingEns.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox apiServerBox;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\SettingEns.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock lblSmtpServer;
        
        #line default
        #line hidden
        
        
        #line 139 "..\..\SettingEns.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox smtpServerBox;
        
        #line default
        #line hidden
        
        
        #line 141 "..\..\SettingEns.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock lblSmtpPort;
        
        #line default
        #line hidden
        
        
        #line 142 "..\..\SettingEns.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox smtpPortBox;
        
        #line default
        #line hidden
        
        
        #line 144 "..\..\SettingEns.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock lblSmtpUsername;
        
        #line default
        #line hidden
        
        
        #line 145 "..\..\SettingEns.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox smtpUsernameBox;
        
        #line default
        #line hidden
        
        
        #line 147 "..\..\SettingEns.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock lblSmtpPassword;
        
        #line default
        #line hidden
        
        
        #line 148 "..\..\SettingEns.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.PasswordBox smtpPasswordBox;
        
        #line default
        #line hidden
        
        
        #line 150 "..\..\SettingEns.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock lblSmtpSsl;
        
        #line default
        #line hidden
        
        
        #line 151 "..\..\SettingEns.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox smtpSslCheckBox;
        
        #line default
        #line hidden
        
        
        #line 155 "..\..\SettingEns.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button okBtn;
        
        #line default
        #line hidden
        
        
        #line 156 "..\..\SettingEns.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button returnBtn;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/CtrEmail;component/settingens.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\SettingEns.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.listStack = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 2:
            this.datagrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 3:
            
            #line 39 "..\..\SettingEns.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.onAddBtn);
            
            #line default
            #line hidden
            return;
            case 4:
            
            #line 40 "..\..\SettingEns.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.onModBtn);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 41 "..\..\SettingEns.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.onDelBtn);
            
            #line default
            #line hidden
            return;
            case 6:
            this.textStack = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 7:
            this.nameBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.ipBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.usernameBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.passwordBox = ((System.Windows.Controls.PasswordBox)(target));
            return;
            case 11:
            this.sendBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.receiveBox = ((System.Windows.Controls.RichTextBox)(target));
            return;
            case 13:
            this.apiRadio = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 14:
            this.smtpRadio = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 15:
            this.lblApiServer = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.apiServerBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 17:
            this.lblSmtpServer = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.smtpServerBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 19:
            this.lblSmtpPort = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.smtpPortBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 21:
            this.lblSmtpUsername = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.smtpUsernameBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 23:
            this.lblSmtpPassword = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 24:
            this.smtpPasswordBox = ((System.Windows.Controls.PasswordBox)(target));
            return;
            case 25:
            this.lblSmtpSsl = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 26:
            this.smtpSslCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 27:
            this.okBtn = ((System.Windows.Controls.Button)(target));
            
            #line 155 "..\..\SettingEns.xaml"
            this.okBtn.Click += new System.Windows.RoutedEventHandler(this.onOkBtn);
            
            #line default
            #line hidden
            return;
            case 28:
            this.returnBtn = ((System.Windows.Controls.Button)(target));
            
            #line 156 "..\..\SettingEns.xaml"
            this.returnBtn.Click += new System.Windows.RoutedEventHandler(this.onReturnBtn);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

