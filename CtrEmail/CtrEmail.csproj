<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{81E5EB80-18F6-4960-854B-A565E4BF42AC}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>CtrEmail</RootNamespace>
    <AssemblyName>CtrEmail</AssemblyName>
    <TargetFrameworkVersion>v4.7</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <ProjectTypeGuids>{60dc8134-eba5-43b8-bcc9-bb4bc16c2548};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <WarningLevel>4</WarningLevel>
    <Deterministic>true</Deterministic>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>x64</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>BEMAIL.ICO</ApplicationIcon>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="MaterialDesignColors, Version=1.1.2.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\MaterialDesignColors.1.1.2\lib\net45\MaterialDesignColors.dll</HintPath>
    </Reference>
    <Reference Include="MaterialDesignThemes.Wpf, Version=2.5.1.1345, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\MaterialDesignThemes.2.5.1\lib\net45\MaterialDesignThemes.Wpf.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=6.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>D:\test\wpf\WpfApp1\WpfApp1\bin\Debug\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.SQLite">
      <HintPath>D:\Program Files\System.Data.SQLite\2012\bin\System.Data.SQLite.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Windows.Interactivity, Version=4.5.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>D:\test\wpf\WpfApp1\WpfApp1\bin\Debug\System.Windows.Interactivity.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xaml">
      <RequiredTargetFramework>4.0</RequiredTargetFramework>
    </Reference>
    <Reference Include="WindowsBase" />
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
  </ItemGroup>
  <ItemGroup>
    <ApplicationDefinition Include="App.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </ApplicationDefinition>
    <Compile Include="BackupTask.cs" />
    <Compile Include="Connected Services\ServiceReference1\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="Connected Services\ServiceReference2\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="Connected Services\WebReference\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="CtrList.xaml.cs">
      <DependentUpon>CtrList.xaml</DependentUpon>
    </Compile>
    <Compile Include="EmailLog.xaml.cs">
      <DependentUpon>EmailLog.xaml</DependentUpon>
    </Compile>
    <Compile Include="DbAdapter.cs" />
    <Compile Include="Pager.xaml.cs">
      <DependentUpon>Pager.xaml</DependentUpon>
    </Compile>
    <Compile Include="ProgressBarControl.xaml.cs">
      <DependentUpon>ProgressBarControl.xaml</DependentUpon>
    </Compile>
    <Compile Include="SettingDb.xaml.cs">
      <DependentUpon>SettingDb.xaml</DependentUpon>
    </Compile>
    <Compile Include="SettingEns.xaml.cs">
      <DependentUpon>SettingEns.xaml</DependentUpon>
    </Compile>
    <Compile Include="SettingPara.xaml.cs">
      <DependentUpon>SettingPara.xaml</DependentUpon>
    </Compile>
    <Compile Include="SettingEmail.xaml.cs">
      <DependentUpon>SettingEmail.xaml</DependentUpon>
    </Compile>
    <Compile Include="SysLog.xaml.cs">
      <DependentUpon>SysLog.xaml</DependentUpon>
    </Compile>
    <Compile Include="UserControlCustomers.xaml.cs">
      <DependentUpon>UserControlCustomers.xaml</DependentUpon>
    </Compile>
    <Compile Include="UserControlMenuItem.xaml.cs">
      <DependentUpon>UserControlMenuItem.xaml</DependentUpon>
    </Compile>
    <Compile Include="UserControlProviders.xaml.cs">
      <DependentUpon>UserControlProviders.xaml</DependentUpon>
    </Compile>
    <Compile Include="Utils\EnsLog.cs" />
    <Compile Include="Utils\Entities.cs" />
    <Compile Include="Utils\Utils.cs" />
    <Compile Include="Utils\Webservice.cs" />
    <Compile Include="Utils\XmlConfig.cs" />
    <Compile Include="ViewModel\CtroperationlogModel.cs" />
    <Compile Include="ViewModel\EmaillogModel.cs" />
    <Compile Include="ViewModel\EnsModel.cs" />
    <Compile Include="ViewModel\ItemMenu.cs" />
    <Compile Include="ViewModel\SubItem.cs" />
    <Compile Include="ViewModel\SyslogModel.cs" />
    <Compile Include="Web References\WebReference\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.map</DependentUpon>
    </Compile>
    <Page Include="CtrList.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="EmailLog.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="MainWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Compile Include="App.xaml.cs">
      <DependentUpon>App.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="MainWindow.xaml.cs">
      <DependentUpon>MainWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Page Include="Pager.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="ProgressBarControl.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="SettingDb.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="SettingEns.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="SettingPara.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="SettingEmail.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="SysLog.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="UserControlCustomers.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="UserControlMenuItem.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="UserControlProviders.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <None Include="Connected Services\ServiceReference1\wsdl.wsdl" />
    <None Include="Connected Services\ServiceReference2\wsdl.wsdl" />
    <None Include="Connected Services\WebReference\wsdl.wsdl" />
    <None Include="packages.config">
      <SubType>Designer</SubType>
    </None>
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <None Include="Web References\WebReference\Reference.map">
      <Generator>MSDiscoCodeGenerator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
    <None Include="Web References\WebReference\wsdl.wsdl" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\logo.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\login_logo.jpg" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Connected Services\" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadataStorage Include="Connected Services\ServiceReference1\" />
    <WCFMetadataStorage Include="Connected Services\ServiceReference2\" />
    <WCFMetadataStorage Include="Connected Services\WebReference\" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Connected Services\WebReference\configuration91.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Connected Services\WebReference\configuration.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Connected Services\WebReference\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="Connected Services\ServiceReference1\configuration91.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Connected Services\ServiceReference1\configuration.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Connected Services\ServiceReference1\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <WebReferences Include="Web References\" />
  </ItemGroup>
  <ItemGroup>
    <WebReferenceUrl Include="http://192.168.1.157/nav/system/employee/soapserver%3fwsdl">
      <UrlBehavior>Dynamic</UrlBehavior>
      <RelPath>Web References\WebReference\</RelPath>
      <UpdateFromURL>http://192.168.1.157/nav/system/employee/soapserver%3fwsdl</UpdateFromURL>
      <ServiceLocationURL>
      </ServiceLocationURL>
      <CachedDynamicPropName>
      </CachedDynamicPropName>
      <CachedAppSettingsObjectName>Settings</CachedAppSettingsObjectName>
      <CachedSettingsPropName>DropDownMenu_WebReference_wsdlService</CachedSettingsPropName>
    </WebReferenceUrl>
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <None Include="Connected Services\ServiceReference2\configuration91.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Connected Services\ServiceReference2\configuration.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Connected Services\ServiceReference2\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Resource Include="BEMAIL~3.ICO" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="BEMAIL.ICO" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>
