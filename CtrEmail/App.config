<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <configSections>
        <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" >
            <section name="CtrEmail.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
        </sectionGroup>
    </configSections>
    <startup> 
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7" />
    </startup>
<system.serviceModel>
        <bindings>
                <basicHttpBinding>
                        <binding name="ServiceBinding" />
                        <binding name="ServiceBinding1" />
                        <binding name="ServiceBinding2" />
                </basicHttpBinding>
        </bindings>
        <client>
                <endpoint address="http://127.0.0.1/nav/system/employee/soapserver"
                        binding="basicHttpBinding" bindingConfiguration="ServiceBinding"
                        contract="WebReference.ServicePortType" name="ServicePort" />
                <endpoint address="http://127.0.0.1/nav/system/employee/soapserver"
                        binding="basicHttpBinding" bindingConfiguration="ServiceBinding1"
                        contract="ServiceReference1.ServicePortType" name="ServicePort1" />
                <endpoint address="http://127.0.0.1/nav/system/employee/soapserver"
                        binding="basicHttpBinding" bindingConfiguration="ServiceBinding2"
                        contract="ServiceReference2.ServicePortType" name="ServicePort2" />
        </client>
    </system.serviceModel>
    <applicationSettings>
        <CtrEmail.Properties.Settings>
            <setting name="CtrEmail_WebReference_wsdlService" serializeAs="String">
                <value>http://127.0.0.1/nav/system/employee/soapserver</value>
            </setting>
            <setting name="DropDownMenu_WebReference_wsdlService" serializeAs="String">
                <value>http://127.0.0.1/nav/system/employee/soapserver</value>
            </setting>
        </CtrEmail.Properties.Settings>
    </applicationSettings>
</configuration>