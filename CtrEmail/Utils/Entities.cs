using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CtrEmail
{
    

    public class Controller : INotifyPropertyChanged //控制器
    {
        private int id;
        private int typeid;
        private string ipaddress;
        private int ctraddress;
        private string location;
        private string username;
        private string password;
        private string status;

        public event PropertyChangedEventHandler PropertyChanged;

        public Controller()
        {

        }

        public int Id
        {
            get { return id; }
            set
            {
                id = value;
                if(PropertyChanged != null)
                {
                    PropertyChanged(this, new PropertyChangedEventArgs("id"));
                }
                
            }
        }

        
        public string Status
        {
            get { return status; }
            set
            {
                if (string.IsNullOrEmpty(value))
                {
                    status = "未知";
                }
                else
                {
                    status = value;
                }
                if (PropertyChanged != null)
                {
                    PropertyChanged(this, new PropertyChangedEventArgs("status"));
                }
            }
        }

        public int Typeid
        {
            get { return typeid; }
            set
            {
                typeid = value;
                if (PropertyChanged != null)
                {
                    PropertyChanged(this, new PropertyChangedEventArgs("typeid"));
                }
            }
        }

        public string Ipaddress
        {
            get { return ipaddress; }
            set
            {
                ipaddress = value;
                if (PropertyChanged != null)
                {
                    PropertyChanged(this, new PropertyChangedEventArgs("ipaddress"));
                }
            }
        }

        public int Ctraddress
        {
            get { return ctraddress; }
            set
            {
                ctraddress = value;
                if (PropertyChanged != null)
                {
                    PropertyChanged(this, new PropertyChangedEventArgs("ctraddress"));
                }
            }
        }

        public string Location
        {
            get { return location; }
            set
            {
                location = value;
                if (PropertyChanged != null)
                {
                    PropertyChanged(this, new PropertyChangedEventArgs("location"));
                }
            }
        }

        public string Name
        {
            get
            {
                if (!string.IsNullOrEmpty(this.Location))
                {
                    return this.Ctraddress.ToString() + " " + this.Location;
                }
                else
                {
                    return this.Ctraddress.ToString();
                }
            }
        }
        public string Username
        {
            get { return username; }
            set
            {
                username = value;
                if (PropertyChanged != null)
                {
                    PropertyChanged(this, new PropertyChangedEventArgs("username"));
                }
            }
        }

        public string Password
        {
            get { return password; }
            set
            {
                password = value;
                if (PropertyChanged != null)
                {
                    PropertyChanged(this, new PropertyChangedEventArgs("password"));
                }
            }
        }

        public ObservableCollection<CardAuth> cardauthList = new ObservableCollection<CardAuth>();
    }

    public class CardAuth : INotifyPropertyChanged
    {
        public event PropertyChangedEventHandler PropertyChanged;

        private string _cardno;
        public string Cardno
        {
            get
            {
                return _cardno;
            }
            set
            {
                if (_cardno != value)
                {
                    _cardno = value;
                    if (PropertyChanged != null)
                        PropertyChanged(this, new PropertyChangedEventArgs("Cardno"));
                }
            }
        }


        private ObservableCollection<Door> _doors;


        public ObservableCollection<Door> Doors
        {
            get
            {
                return _doors;
            }
            set
            {
                if (_doors != value)
                {
                    _doors = value;

                    if (PropertyChanged != null)
                        PropertyChanged(this, new PropertyChangedEventArgs("Doors"));
                }
            }
        }

        public void SortDoors()
        {
            int count = _doors.Count;
            for(int i = 0; i < count - 1; i++)
            {
                for(int j = 0; j < count - 1 - i; j++)
                {
                    if (_doors[j].Doorid > _doors[j + 1].Doorid)
                    {
                        Door door = _doors[j];
                        _doors[j] = _doors[j + 1];
                        _doors[j + 1] = door;
                    }
                }
            }
        }

        public CardAuth()
        {
            _doors = new ObservableCollection<Door>();
        }
    }

    public class Doorzone : INotifyPropertyChanged
    {
        public int Id
        {
            get; set;
        }

        private string _name;
        public string Name
        {
            get
            {
                return _name;
            }
            set
            {
                if (_name != value)
                {
                    _name = value;
                    if (PropertyChanged != null)
                    {
                        PropertyChanged.Invoke(this, new PropertyChangedEventArgs("Doorzone"));
                    }
                }
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;
    }

    public class Door : INotifyPropertyChanged
    {
        public event PropertyChangedEventHandler PropertyChanged;
        public int Doorid { get; set; }

        private string _location;
        public string Location
        {
            get
            {
                return _location;
            }
            set
            {
                if (_location != value)
                {
                    _location = value;
                    if (PropertyChanged != null)
                    {
                        PropertyChanged.Invoke(this, new PropertyChangedEventArgs("Door"));
                    }
                }
            }
        }

        private int _doorno;
        public int Doorno
        {
            get
            {
                return _doorno;
            }
            set
            {
                if(_doorno != value)
                {
                    _doorno = value;
                    if(PropertyChanged != null)
                    {
                        PropertyChanged.Invoke(this, new PropertyChangedEventArgs("Doorno"));
                    }
                }
            }
        }
    }


    public enum DbAuthEnum
    {
        WindowsAuth = 0,
        SqlAuth = 1
    }
}
