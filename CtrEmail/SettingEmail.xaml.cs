using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using CtrEmail.Utils;
using System.Net.Mail;
using System.Net;

namespace CtrEmail
{
    /// <summary>
    /// SettingEmail.xaml 的交互逻辑
    /// </summary>
    public partial class SettingEmail : UserControl
    {
        public SettingEmail()
        {
            InitializeComponent();
            LoadConfiguration();
        }

        /// <summary>
        /// 加载配置
        /// </summary>
        private void LoadConfiguration()
        {
            try
            {
                // 加载发送方式配置
                string sendMode = XmlConfig.getValue("emailsendmode");

                // 设置发送方式
                if (sendMode == "smtp")
                {
                    smtpRadio.IsChecked = true;
                    ShowSmtpControls(true);
                    ShowApiControls(false);
                    LoadSmtpConfiguration();
                }
                else
                {
                    apiRadio.IsChecked = true;
                    ShowSmtpControls(false);
                    ShowApiControls(true);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("加载配置失败: " + ex.Message, "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 加载SMTP配置
        /// </summary>
        private void LoadSmtpConfiguration()
        {
            try
            {
                smtpServerBox.Text = XmlConfig.getValue("smtpserver");
                smtpPortBox.Text = XmlConfig.getValue("smtpport");
                smtpUsernameBox.Text = XmlConfig.getValue("smtpusername");
                smtpPasswordBox.Password = XmlConfig.getValue("smtppassword");
                smtpSslCheckBox.IsChecked = XmlConfig.getValue("smtpssl") == "true";
            }
            catch (Exception ex)
            {
                MessageBox.Show("加载SMTP配置失败: " + ex.Message, "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 发送方式改变事件
        /// </summary>
        private void OnSendModeChanged(object sender, RoutedEventArgs e)
        {
            if (apiRadio.IsChecked == true)
            {
                ShowApiControls(true);
                ShowSmtpControls(false);
            }
            else if (smtpRadio.IsChecked == true)
            {
                ShowApiControls(false);
                ShowSmtpControls(true);
                LoadSmtpConfiguration();
            }
        }

        /// <summary>
        /// 显示或隐藏API控件
        /// </summary>
        private void ShowApiControls(bool show)
        {
            var visibility = show ? Visibility.Visible : Visibility.Collapsed;
            lblApiServer.Visibility = visibility;
            apiServerBox.Visibility = visibility;
        }

        /// <summary>
        /// 显示或隐藏SMTP控件
        /// </summary>
        private void ShowSmtpControls(bool show)
        {
            var visibility = show ? Visibility.Visible : Visibility.Collapsed;
            lblSmtpServer.Visibility = visibility;
            smtpServerBox.Visibility = visibility;
            lblSmtpPort.Visibility = visibility;
            smtpPortBox.Visibility = visibility;
            lblSmtpUsername.Visibility = visibility;
            smtpUsernameBox.Visibility = visibility;
            lblSmtpPassword.Visibility = visibility;
            smtpPasswordBox.Visibility = visibility;
            lblSmtpSsl.Visibility = visibility;
            smtpSslCheckBox.Visibility = visibility;
        }

        /// <summary>
        /// 测试连接
        /// </summary>
        private void OnTestConnection(object sender, RoutedEventArgs e)
        {
            if (smtpRadio.IsChecked == true)
            {
                TestSmtpConnection();
            }
            else
            {
                MessageBox.Show("API接口连接测试功能暂未实现", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        /// <summary>
        /// 测试SMTP连接
        /// </summary>
        private void TestSmtpConnection()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(smtpServerBox.Text) ||
                    string.IsNullOrWhiteSpace(smtpPortBox.Text) ||
                    string.IsNullOrWhiteSpace(smtpUsernameBox.Text) ||
                    string.IsNullOrWhiteSpace(smtpPasswordBox.Password))
                {
                    MessageBox.Show("请填写完整的SMTP配置信息", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                int port;
                if (!int.TryParse(smtpPortBox.Text, out port))
                {
                    MessageBox.Show("端口号格式不正确", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                using (SmtpClient client = new SmtpClient(smtpServerBox.Text, port))
                {
                    client.EnableSsl = smtpSslCheckBox.IsChecked == true;
                    client.Credentials = new NetworkCredential(smtpUsernameBox.Text, smtpPasswordBox.Password);
                    client.Timeout = 10000; // 10秒超时

                    // 创建测试邮件
                    MailMessage testMail = new MailMessage();
                    testMail.From = new MailAddress(smtpUsernameBox.Text);
                    testMail.To.Add(smtpUsernameBox.Text); // 发送给自己
                    testMail.Subject = "SMTP连接测试";
                    testMail.Body = "这是一封SMTP连接测试邮件，如果您收到此邮件，说明SMTP配置正确。";
                    testMail.IsBodyHtml = false;

                    client.Send(testMail);
                    MessageBox.Show("SMTP连接测试成功！测试邮件已发送。", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("SMTP连接测试失败: " + ex.Message, "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 保存配置
        /// </summary>
        private void OnSaveConfig(object sender, RoutedEventArgs e)
        {
            try
            {
                // 验证输入
                if (!ValidateInput())
                {
                    return;
                }

                // 保存发送方式
                if (smtpRadio.IsChecked == true)
                {
                    XmlConfig.addValue("emailsendmode", "smtp");
                    SaveSmtpConfiguration();
                }
                else
                {
                    XmlConfig.addValue("emailsendmode", "api");
                }

                MessageBox.Show("配置保存成功！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show("保存配置失败: " + ex.Message, "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 保存SMTP配置
        /// </summary>
        private void SaveSmtpConfiguration()
        {
            XmlConfig.addValue("smtpserver", smtpServerBox.Text);
            XmlConfig.addValue("smtpport", smtpPortBox.Text);
            XmlConfig.addValue("smtpusername", smtpUsernameBox.Text);
            XmlConfig.addValue("smtppassword", smtpPasswordBox.Password);
            XmlConfig.addValue("smtpssl", smtpSslCheckBox.IsChecked == true ? "true" : "false");
        }

        /// <summary>
        /// 验证输入
        /// </summary>
        private bool ValidateInput()
        {
            // 如果选择SMTP方式，验证SMTP配置
            if (smtpRadio.IsChecked == true)
            {
                if (string.IsNullOrWhiteSpace(smtpServerBox.Text))
                {
                    MessageBox.Show("请输入SMTP服务器地址", "输入错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    smtpServerBox.Focus();
                    return false;
                }

                int port;
                if (!int.TryParse(smtpPortBox.Text, out port) || port <= 0 || port > 65535)
                {
                    MessageBox.Show("SMTP端口必须是1-65535之间的整数", "输入错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    smtpPortBox.Focus();
                    return false;
                }

                if (string.IsNullOrWhiteSpace(smtpUsernameBox.Text))
                {
                    MessageBox.Show("请输入SMTP用户名", "输入错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    smtpUsernameBox.Focus();
                    return false;
                }

                if (string.IsNullOrWhiteSpace(smtpPasswordBox.Password))
                {
                    MessageBox.Show("请输入SMTP密码", "输入错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    smtpPasswordBox.Focus();
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 重置配置
        /// </summary>
        private void OnResetConfig(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("确定要重置所有配置吗？这将恢复默认设置。", "确认", 
                MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                // 重置为默认值
                apiRadio.IsChecked = true;
                smtpServerBox.Text = "";
                smtpPortBox.Text = "587";
                smtpUsernameBox.Text = "";
                smtpPasswordBox.Password = "";
                smtpSslCheckBox.IsChecked = true;
                
                ShowApiControls(true);
                ShowSmtpControls(false);
            }
        }
    }
}
