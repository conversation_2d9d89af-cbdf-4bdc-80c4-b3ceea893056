using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CtrEmail.ViewModel
{
    public class CtroperationlogItem : INotifyPropertyChanged
    {
        public event PropertyChangedEventHandler PropertyChanged;

        private string _logtime;
        private string _username;
        private string _cardno;
        private string _doorlocation;
        private string _content;

        public string Logtime
        {
            get { return _logtime; }
            set
            {
                _logtime = value;
                if (PropertyChanged != null)
                {
                    PropertyChanged(this, new PropertyChangedEventArgs("logtime"));
                }
            }
        }

        public string Username
        {
            get { return _username; }
            set
            {
                _username = value;
                if (PropertyChanged != null)
                {
                    PropertyChanged(this, new PropertyChangedEventArgs("username"));
                }
            }
        }

        public string Cardno
        {
            get { return _cardno; }
            set
            {
                _cardno = value;
                if (PropertyChanged != null)
                {
                    PropertyChanged(this, new PropertyChangedEventArgs("cardno"));
                }
            }
        }

        public string Doorlocation
        {
            get { return _doorlocation; }
            set
            {
                _doorlocation = value;
                if (PropertyChanged != null)
                {
                    PropertyChanged(this, new PropertyChangedEventArgs("doorlocation"));
                }
            }
        }

        public string Content
        {
            get { return _content; }
            set
            {
                _content = value;
                if (PropertyChanged != null)
                {
                    PropertyChanged(this, new PropertyChangedEventArgs("content"));
                }
            }
        }



        public CtroperationlogItem(string _logtime, string _username,string _cardno,string _doorlocation, string _content)
        {
            Logtime = _logtime;
            Username = _username;
            Cardno = _cardno;
            Doorlocation = _doorlocation;
            Content = _content;
        }
    }
    public class CtroperationlogSource
    {
        public int Count;

        public CtroperationlogSource()
        {
            UpdateCount();
        }

        public ObservableCollection<CtroperationlogItem> Take(int itemcountInt, int skipInt)
        {
            UpdateCount();
            ObservableCollection<CtroperationlogItem> resultList = DbAdapter.GetInstance().GetCtroperationlog(itemcountInt, skipInt);


            return resultList;
        }

        public void UpdateCount()
        {
            Count = DbAdapter.GetInstance().GetCtroperationlogCount();
        }
    }

}
