using CtrEmail.ViewModel;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace CtrEmail
{
    /// <summary>
    /// SettingEns.xaml 的交互逻辑
    /// </summary>
    public partial class SettingEns : UserControl
    {
        EnsModel ensModel = new EnsModel();
        MainWindow mainWin;
        public SettingEns()
        {
            InitializeComponent();
            init();

            // 延迟绑定事件，确保控件已完全加载
            this.Loaded += SettingEns_Loaded;
        }

        private void SettingEns_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // 使用FindName获取控件并绑定事件
                var apiRadio = this.FindName("apiRadio") as RadioButton;
                var smtpRadio = this.FindName("smtpRadio") as RadioButton;

                if (apiRadio != null)
                {
                    apiRadio.Checked += (s, args) => {
                        ToggleControls(true); // 显示API，隐藏SMTP
                    };
                }

                if (smtpRadio != null)
                {
                    smtpRadio.Checked += (s, args) => {
                        ToggleControls(false); // 隐藏API，显示SMTP
                    };
                }

                // 初始化显示状态 - 默认显示API，隐藏SMTP
                ToggleControls(true);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("绑定动态切换事件失败: " + ex.Message);
            }
        }

        // 切换控件显示状态
        private void ToggleControls(bool showApi)
        {
            try
            {
                var apiVisibility = showApi ? System.Windows.Visibility.Visible : System.Windows.Visibility.Collapsed;
                var smtpVisibility = showApi ? System.Windows.Visibility.Collapsed : System.Windows.Visibility.Visible;

                // API控件
                var lblApiServer = this.FindName("lblApiServer") as TextBlock;
                var apiServerBox = this.FindName("apiServerBox") as TextBox;
                if (lblApiServer != null) lblApiServer.Visibility = apiVisibility;
                if (apiServerBox != null) apiServerBox.Visibility = apiVisibility;

                // SMTP控件
                var lblSmtpServer = this.FindName("lblSmtpServer") as TextBlock;
                var smtpServerBox = this.FindName("smtpServerBox") as TextBox;
                var lblSmtpPort = this.FindName("lblSmtpPort") as TextBlock;
                var smtpPortBox = this.FindName("smtpPortBox") as TextBox;
                var lblSmtpUsername = this.FindName("lblSmtpUsername") as TextBlock;
                var smtpUsernameBox = this.FindName("smtpUsernameBox") as TextBox;
                var lblSmtpPassword = this.FindName("lblSmtpPassword") as TextBlock;
                var smtpPasswordBox = this.FindName("smtpPasswordBox") as PasswordBox;
                var lblSmtpSsl = this.FindName("lblSmtpSsl") as TextBlock;
                var smtpSslCheckBox = this.FindName("smtpSslCheckBox") as CheckBox;

                if (lblSmtpServer != null) lblSmtpServer.Visibility = smtpVisibility;
                if (smtpServerBox != null) smtpServerBox.Visibility = smtpVisibility;
                if (lblSmtpPort != null) lblSmtpPort.Visibility = smtpVisibility;
                if (smtpPortBox != null) smtpPortBox.Visibility = smtpVisibility;
                if (lblSmtpUsername != null) lblSmtpUsername.Visibility = smtpVisibility;
                if (smtpUsernameBox != null) smtpUsernameBox.Visibility = smtpVisibility;
                if (lblSmtpPassword != null) lblSmtpPassword.Visibility = smtpVisibility;
                if (smtpPasswordBox != null) smtpPasswordBox.Visibility = smtpVisibility;
                if (lblSmtpSsl != null) lblSmtpSsl.Visibility = smtpVisibility;
                if (smtpSslCheckBox != null) smtpSslCheckBox.Visibility = smtpVisibility;

                System.Diagnostics.Debug.WriteLine("控件切换完成: " + (showApi ? "显示API" : "显示SMTP"));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("切换控件失败: " + ex.Message);
            }
        }

        private void init()
        {
            App app = Application.Current as App;
            mainWin = app.mainWin;

            this.DataContext = ensModel;
            mainWin.ensList = ensModel.DatagridSource;
            mainWin.ensDatagrid = datagrid;
        }

        private void onAddBtn(object sender, RoutedEventArgs e)
        {
            ipBox.IsReadOnly = false;
            nameBox.Text = "";
            ipBox.Text = "";
            usernameBox.Text = "";
            passwordBox.Password = "";
            sendBox.Text = "";

            // 重置SMTP配置
            var apiRadio = this.FindName("apiRadio") as RadioButton;
            var smtpRadio = this.FindName("smtpRadio") as RadioButton;
            var smtpServerBox = this.FindName("smtpServerBox") as TextBox;
            var smtpPortBox = this.FindName("smtpPortBox") as TextBox;
            var smtpUsernameBox = this.FindName("smtpUsernameBox") as TextBox;
            var smtpPasswordBox = this.FindName("smtpPasswordBox") as PasswordBox;
            var smtpSslCheckBox = this.FindName("smtpSslCheckBox") as CheckBox;

            if (apiRadio != null)
            {
                apiRadio.IsChecked = true;
                if (smtpRadio != null) smtpRadio.IsChecked = false;
                if (smtpServerBox != null) smtpServerBox.Text = "";
                if (smtpPortBox != null) smtpPortBox.Text = "587";
                if (smtpUsernameBox != null) smtpUsernameBox.Text = "";
                if (smtpPasswordBox != null) smtpPasswordBox.Password = "";
                if (smtpSslCheckBox != null) smtpSslCheckBox.IsChecked = true;

                // 默认显示API字段，隐藏SMTP字段
                ToggleControls(true);
            }
            
            TextRange textRange1 = new TextRange(receiveBox.Document.ContentStart, receiveBox.Document.ContentEnd);
            textRange1.Text = "";
            //TextRange textRange2 = new TextRange(emaincontentBox.Document.ContentStart, emaincontentBox.Document.ContentEnd);
            //textRange2.Text = "";

            this.listStack.Visibility = Visibility.Collapsed;
            this.textStack.Visibility = Visibility.Visible;
            isAdd = true;
        }

        private bool isAdd = false;
        private void onModBtn(object sender, RoutedEventArgs e)
        {
            int indexInt = datagrid.SelectedIndex;
            if (indexInt < 0)
            {
                MessageBox.Show("请选择");
                return;
            }
            isAdd = false;
            this.listStack.Visibility = Visibility.Collapsed;
            this.textStack.Visibility = Visibility.Visible;

            EnsItem ens = ensModel.DatagridSource[indexInt];
            this.nameBox.Text = ens.Ensname;
            ipBox.Text = ens.Ip;
            ipBox.IsReadOnly = true;
            usernameBox.Text = ens.Username;
            passwordBox.Password = ens.Password;
            sendBox.Text = ens.Emailfrom;

            TextRange textRange1 = new TextRange(receiveBox.Document.ContentStart, receiveBox.Document.ContentEnd);
            textRange1.Text = ens.Emailto;
            //TextRange textRange2 = new TextRange(emaincontentBox.Document.ContentStart, emaincontentBox.Document.ContentEnd);
            //textRange2.Text = ens.Emailcontent;

            // 加载SMTP配置
            var apiRadio = this.FindName("apiRadio") as RadioButton;
            var smtpRadio = this.FindName("smtpRadio") as RadioButton;
            var smtpServerBox = this.FindName("smtpServerBox") as TextBox;
            var smtpPortBox = this.FindName("smtpPortBox") as TextBox;
            var smtpUsernameBox = this.FindName("smtpUsernameBox") as TextBox;
            var smtpPasswordBox = this.FindName("smtpPasswordBox") as PasswordBox;
            var smtpSslCheckBox = this.FindName("smtpSslCheckBox") as CheckBox;

            if (apiRadio != null && smtpRadio != null)
            {
                if (ens.Emailtype == 1)
                {
                    smtpRadio.IsChecked = true;
                    apiRadio.IsChecked = false;
                    ToggleControls(false); // 显示SMTP
                }
                else
                {
                    apiRadio.IsChecked = true;
                    smtpRadio.IsChecked = false;
                    ToggleControls(true); // 显示API
                }

                if (smtpServerBox != null) smtpServerBox.Text = ens.Smtpserver ?? "";
                if (smtpPortBox != null) smtpPortBox.Text = ens.Smtpport.ToString();
                if (smtpUsernameBox != null) smtpUsernameBox.Text = ens.Smtpusername ?? "";
                if (smtpPasswordBox != null) smtpPasswordBox.Password = ens.Smtppassword ?? "";
                if (smtpSslCheckBox != null) smtpSslCheckBox.IsChecked = ens.Smtpssl;
            }

        }

        private void onDelBtn(object sender, RoutedEventArgs e)
        {
            int indexInt = datagrid.SelectedIndex;
            if (indexInt < 0)
            {
                MessageBox.Show("请选择");
                return;
            }
            if(MessageBox.Show("确定删除吗?","", MessageBoxButton.YesNo) == MessageBoxResult.Yes)
            {
                bool resultBl = ensModel.DelEns(ensModel.DatagridSource[indexInt]);
                ensModel.DatagridSource.RemoveAt(indexInt);
                if (resultBl)
                {
                    MessageBox.Show("删除成功");
                }
            }
        }

        private void onOkBtn(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrEmpty(this.ipBox.Text))
            {
                MessageBox.Show("请输入IP");
                this.ipBox.Focus();
                return;
            }
            if (string.IsNullOrEmpty(this.usernameBox.Text))
            {
                MessageBox.Show("请输入用户名");
                this.usernameBox.Focus();
                return;
            }
            string name = nameBox.Text;
            string ip = this.ipBox.Text;
            string username = this.usernameBox.Text;
            string password = this.passwordBox.Password;
            string send = this.sendBox.Text;
            TextRange textRange1 = new TextRange(receiveBox.Document.ContentStart, receiveBox.Document.ContentEnd);
            string receive = System.Text.RegularExpressions.Regex.Replace(textRange1.Text, "[\r\n]", "");

            //TextRange textRange2 = new TextRange(emaincontentBox.Document.ContentStart, emaincontentBox.Document.ContentEnd);
            //string emailcontentStr = textRange2.Text;

            // 获取SMTP配置
            var smtpRadio = this.FindName("smtpRadio") as RadioButton;
            var smtpServerBox = this.FindName("smtpServerBox") as TextBox;
            var smtpPortBox = this.FindName("smtpPortBox") as TextBox;
            var smtpUsernameBox = this.FindName("smtpUsernameBox") as TextBox;
            var smtpPasswordBox = this.FindName("smtpPasswordBox") as PasswordBox;
            var smtpSslCheckBox = this.FindName("smtpSslCheckBox") as CheckBox;

            int emailtype = (smtpRadio != null && smtpRadio.IsChecked == true) ? 1 : 0;
            string smtpserver = smtpServerBox != null ? smtpServerBox.Text ?? "" : "";
            int smtpport = 587;
            if (smtpPortBox != null) int.TryParse(smtpPortBox.Text, out smtpport);
            string smtpusername = smtpUsernameBox != null ? smtpUsernameBox.Text ?? "" : "";
            string smtppassword = smtpPasswordBox != null ? smtpPasswordBox.Password ?? "" : "";
            bool smtpssl = smtpSslCheckBox != null && smtpSslCheckBox.IsChecked == true;

            // 如果选择SMTP方式，验证SMTP配置
            if (emailtype == 1)
            {
                if (string.IsNullOrEmpty(smtpserver))
                {
                    MessageBox.Show("请输入SMTP服务器");
                    if (smtpServerBox != null) smtpServerBox.Focus();
                    return;
                }
                if (string.IsNullOrEmpty(smtpusername))
                {
                    MessageBox.Show("请输入SMTP用户名");
                    if (smtpUsernameBox != null) smtpUsernameBox.Focus();
                    return;
                }
                if (string.IsNullOrEmpty(smtppassword))
                {
                    MessageBox.Show("请输入SMTP密码");
                    if (smtpPasswordBox != null) smtpPasswordBox.Focus();
                    return;
                }
            }

            ViewModel.EnsItem ensItem = new ViewModel.EnsItem(0, ip, username, password, name, send, receive, "",
                                                            emailtype, smtpserver, smtpport, smtpusername, smtppassword, smtpssl);

            // 调试信息
            string debugInfo = string.Format("保存数据:\n名称: {0}\nIP: {1}\n用户名: {2}\n发送方式: {3}\nSMTP服务器: {4}\nSMTP端口: {5}\nSMTP用户名: {6}",
                name, ip, username, emailtype == 1 ? "SMTP" : "API", smtpserver, smtpport, smtpusername);
            System.Diagnostics.Debug.WriteLine(debugInfo);

            if (isAdd)
            {
                int idInt = ensModel.AddEns(ensItem);
                ensItem.Id = idInt;
                if (ensItem.Id > 0)
                {
                    MessageBox.Show("增加成功，ID: " + idInt);
                }
                else
                {
                    MessageBox.Show("增加失败，请检查日志");
                }
            }
            else
            {
                int resultInt = ensModel.ModEns(ensItem);
                if (resultInt > 0)
                {
                    MessageBox.Show("修改成功，影响行数: " + resultInt);
                }
                else
                {
                    MessageBox.Show("修改失败，请检查日志");
                }
            }
            
            this.listStack.Visibility = Visibility.Visible;
            this.textStack.Visibility = Visibility.Collapsed;
        }

        private void onReturnBtn(object sender, RoutedEventArgs e)
        {
            this.listStack.Visibility = Visibility.Visible;
            this.textStack.Visibility = Visibility.Collapsed;
        }
    }

    public class EnsModel : BaseViewModel
    {
        private ObservableCollection<ViewModel.EnsItem> _datagridSoruce;// = new ObservableCollection<ViewModel.EnsItem>();

        public  ObservableCollection<ViewModel.EnsItem> DatagridSource
        {
            get
            {
                return _datagridSoruce;
            }
            set
            {
                if (_datagridSoruce != value)
                {
                    _datagridSoruce = value;
                    OnPropertyChanged("DatagridSource");
                }
            }
        }

        //private List<EnsItem> _source;
        private ViewModel.EnsSource _source = new ViewModel.EnsSource();

        MainWindow mainWin = null;
        public EnsModel()
        {
            App app = Application.Current as App;
            mainWin = app.mainWin;

            _datagridSoruce = mainWin.ensList;

            _currentPage = 1;

            _source.UpdateCount();
            _totalPage = (int)Math.Ceiling(_source.Count*1.0 / mainWin.pagesizeInt);

            List<ViewModel.EnsItem> result = _source.Take(_source.Count, 0).ToList();

            _datagridSoruce.Clear();

            _datagridSoruce.AddRange(result);

            _firstPageCommand = new DelegateCommand(FirstPageAction);

            _previousPageCommand = new DelegateCommand(PreviousPageAction);

            _nextPageCommand = new DelegateCommand(NextPageAction);

            _lastPageCommand = new DelegateCommand(LastPageAction);
        }

        public int AddEns(ViewModel.EnsItem ensItem)
        {
            int rowcountInt = _source.Add(ensItem);
            if(rowcountInt > 0)
                this.DatagridSource.Add(ensItem);
            return rowcountInt;
        }

        public int ModEns(ViewModel.EnsItem ensItem)
        {
            for(int i=0;i< DatagridSource.Count; i++)
            {
                if (ensItem.Ip.Equals(DatagridSource[i].Ip))
                {
                    DatagridSource[i] = ensItem;
                }
            }
                
            return _source.Mod(ensItem);
        }

        public bool DelEns(ViewModel.EnsItem ensItem)
        {
            return _source.Del(ensItem);
        }

        private void FirstPageAction()
        {
            CurrentPage = 1;

            var result = _source.Take(mainWin.pagesizeInt, 0).ToList();

            _datagridSoruce.Clear();

            _datagridSoruce.AddRange(result);
        }

        private void PreviousPageAction()
        {
            if (CurrentPage == 1)
            {
                return;
            }

            List<ViewModel.EnsItem> result = new List<ViewModel.EnsItem>();

            int skipnumInt = (CurrentPage - 2) * mainWin.pagesizeInt;
            if (skipnumInt < 0)
                skipnumInt = 0;

            result = _source.Take(mainWin.pagesizeInt, skipnumInt).ToList();

            _datagridSoruce.Clear();

            _datagridSoruce.AddRange(result);

            CurrentPage--;
        }

        private void NextPageAction()
        {
            if (CurrentPage == _totalPage)
            {
                return;
            }

            List<ViewModel.EnsItem> result = new List<ViewModel.EnsItem>();

            result = _source.Take(mainWin.pagesizeInt, CurrentPage * mainWin.pagesizeInt).ToList();

            _datagridSoruce.Clear();

            _datagridSoruce.AddRange(result);

            CurrentPage++;
        }

        private void LastPageAction()
        {
            CurrentPage = TotalPage;

            int skipCount = (_totalPage - 1) * mainWin.pagesizeInt;
            int takeCount = _source.Count - skipCount;

            var result = _source.Take(takeCount, skipCount).ToList();

            _datagridSoruce.Clear();

            _datagridSoruce.AddRange(result);
        }

        // 动态切换功能已在ToggleControls方法中实现
    }
}
