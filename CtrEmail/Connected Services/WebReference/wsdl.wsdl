<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:typens="urn:wsdl" xmlns="http://schemas.xmlsoap.org/wsdl/" name="wsdl" targetNamespace="urn:wsdl" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types />
  <wsdl:message name="login">
    <wsdl:part name="username" type="xsd:string" />
    <wsdl:part name="password" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="loginResponse">
    <wsdl:part name="loginReturn" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="insertReserveCards">
    <wsdl:part name="sessionid" type="xsd:string" />
    <wsdl:part name="cards" type="xsd:string" />
    <wsdl:part name="ip" type="xsd:string" />
    <wsdl:part name="userid" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="insertReserveCardsResponse">
    <wsdl:part name="insertReserveCardsReturn" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="searchReserveCards">
    <wsdl:part name="sessionid" type="xsd:string" />
    <wsdl:part name="cards" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="searchReserveCardsResponse">
    <wsdl:part name="searchReserveCardsReturn" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="deleteReserveCards">
    <wsdl:part name="sessionid" type="xsd:string" />
    <wsdl:part name="cards" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="deleteReserveCardsResponse">
    <wsdl:part name="deleteReserveCardsReturn" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="insertPublicDoors">
    <wsdl:part name="sessionid" type="xsd:string" />
    <wsdl:part name="doorInfo" type="xsd:string" />
    <wsdl:part name="ip" type="xsd:string" />
    <wsdl:part name="userid" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="insertPublicDoorsResponse">
    <wsdl:part name="insertPublicDoorsReturn" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="updatePublicDoors">
    <wsdl:part name="sessionid" type="xsd:string" />
    <wsdl:part name="doorInfo" type="xsd:string" />
    <wsdl:part name="ip" type="xsd:string" />
    <wsdl:part name="userid" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="updatePublicDoorsResponse">
    <wsdl:part name="updatePublicDoorsReturn" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="searchPublicDoors">
    <wsdl:part name="sessionid" type="xsd:string" />
    <wsdl:part name="doorInfo" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="searchPublicDoorsResponse">
    <wsdl:part name="searchPublicDoorsReturn" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="deletePublicDoors">
    <wsdl:part name="sessionid" type="xsd:string" />
    <wsdl:part name="doorInfo" type="xsd:string" />
    <wsdl:part name="ip" type="xsd:string" />
    <wsdl:part name="userid" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="deletePublicDoorsResponse">
    <wsdl:part name="deletePublicDoorsReturn" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="updateDoors">
    <wsdl:part name="sessionid" type="xsd:string" />
    <wsdl:part name="doorInfo" type="xsd:string" />
    <wsdl:part name="ip" type="xsd:string" />
    <wsdl:part name="userid" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="updateDoorsResponse">
    <wsdl:part name="updateDoorsReturn" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="insertEmployees">
    <wsdl:part name="sessionid" type="xsd:string" />
    <wsdl:part name="employees" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="insertEmployeesResponse">
    <wsdl:part name="insertEmployeesReturn" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="deleteEmployees">
    <wsdl:part name="sessionid" type="xsd:string" />
    <wsdl:part name="employees" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="deleteEmployeesResponse">
    <wsdl:part name="deleteEmployeesReturn" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="updateEmployees">
    <wsdl:part name="sessionid" type="xsd:string" />
    <wsdl:part name="employees" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="updateEmployeesResponse">
    <wsdl:part name="updateEmployeesReturn" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="searchEmployees">
    <wsdl:part name="sessionid" type="xsd:string" />
    <wsdl:part name="cards" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="searchEmployeesResponse">
    <wsdl:part name="searchEmployeesReturn" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="insertCards">
    <wsdl:part name="sessionid" type="xsd:string" />
    <wsdl:part name="cards" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="insertCardsResponse">
    <wsdl:part name="insertCardsReturn" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="updateCards">
    <wsdl:part name="sessionid" type="xsd:string" />
    <wsdl:part name="cards" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="updateCardsResponse">
    <wsdl:part name="updateCardsReturn" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="deleteCards">
    <wsdl:part name="sessionid" type="xsd:string" />
    <wsdl:part name="cards" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="deleteCardsResponse">
    <wsdl:part name="deleteCardsReturn" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="searchCards">
    <wsdl:part name="sessionid" type="xsd:string" />
    <wsdl:part name="cards" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="searchCardsResponse">
    <wsdl:part name="searchCardsReturn" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="insertDepts">
    <wsdl:part name="sessionid" type="xsd:string" />
    <wsdl:part name="depts" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="insertDeptsResponse">
    <wsdl:part name="insertDeptsReturn" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="updateDepts">
    <wsdl:part name="sessionid" type="xsd:string" />
    <wsdl:part name="depts" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="updateDeptsResponse">
    <wsdl:part name="updateDeptsReturn" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="deleteDepts">
    <wsdl:part name="sessionid" type="xsd:string" />
    <wsdl:part name="depts" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="deleteDeptsResponse">
    <wsdl:part name="deleteDeptsReturn" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="searchDepts">
    <wsdl:part name="sessionid" type="xsd:string" />
    <wsdl:part name="depts" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="searchDeptsResponse">
    <wsdl:part name="searchDeptsReturn" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="getCardsDoors">
    <wsdl:part name="sessionid" type="xsd:string" />
    <wsdl:part name="card" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="getCardsDoorsResponse">
    <wsdl:part name="getCardsDoorsReturn" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="searchCardsDoors">
    <wsdl:part name="sessionid" type="xsd:string" />
    <wsdl:part name="depts" type="xsd:string" />
    <wsdl:part name="leaders" type="xsd:string" />
    <wsdl:part name="posts" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="searchCardsDoorsResponse">
    <wsdl:part name="searchCardsDoorsReturn" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="asyncSetCardsDoors">
    <wsdl:part name="sessionid" type="xsd:string" />
    <wsdl:part name="cards" type="xsd:string" />
    <wsdl:part name="commandno" type="xsd:int" />
  </wsdl:message>
  <wsdl:message name="asyncSetCardsDoorsResponse">
    <wsdl:part name="asyncSetCardsDoorsReturn" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="setCardsDoors">
    <wsdl:part name="sessionid" type="xsd:string" />
    <wsdl:part name="cards" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="setCardsDoorsResponse">
    <wsdl:part name="setCardsDoorsReturn" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="normalCardeventReport">
    <wsdl:part name="sessionid" type="xsd:string" />
    <wsdl:part name="condition" type="xsd:string" />
    <wsdl:part name="filetype" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="normalCardeventReportResponse">
    <wsdl:part name="normalCardeventReportReturn" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="abnormalCardeventReport">
    <wsdl:part name="sessionid" type="xsd:string" />
    <wsdl:part name="condition" type="xsd:string" />
    <wsdl:part name="filetype" type="xsd:int" />
  </wsdl:message>
  <wsdl:message name="abnormalCardeventReportResponse">
    <wsdl:part name="abnormalCardeventReportReturn" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="doorRelationReport">
    <wsdl:part name="sessionid" type="xsd:string" />
    <wsdl:part name="condition" type="xsd:string" />
    <wsdl:part name="filetype" type="xsd:int" />
  </wsdl:message>
  <wsdl:message name="doorRelationReportResponse">
    <wsdl:part name="doorRelationReportReturn" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="abnormalEmployeeeventReport">
    <wsdl:part name="sessionid" type="xsd:string" />
    <wsdl:part name="condition" type="xsd:string" />
    <wsdl:part name="filetype" type="xsd:int" />
  </wsdl:message>
  <wsdl:message name="abnormalEmployeeeventReportResponse">
    <wsdl:part name="abnormalEmployeeeventReportReturn" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="employeeAuthReport">
    <wsdl:part name="sessionid" type="xsd:string" />
    <wsdl:part name="condition" type="xsd:string" />
    <wsdl:part name="filetype" type="xsd:int" />
  </wsdl:message>
  <wsdl:message name="employeeAuthReportResponse">
    <wsdl:part name="employeeAuthReportReturn" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="doorReport">
    <wsdl:part name="sessionid" type="xsd:string" />
    <wsdl:part name="condition" type="xsd:string" />
    <wsdl:part name="filetype" type="xsd:int" />
  </wsdl:message>
  <wsdl:message name="doorReportResponse">
    <wsdl:part name="doorReportReturn" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="accessLabReport">
    <wsdl:part name="sessionid" type="xsd:string" />
    <wsdl:part name="condition" type="xsd:string" />
    <wsdl:part name="filetype" type="xsd:int" />
  </wsdl:message>
  <wsdl:message name="accessLabReportResponse">
    <wsdl:part name="accessLabReportReturn" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="employeeDoorsReport">
    <wsdl:part name="sessionid" type="xsd:string" />
    <wsdl:part name="condition" type="xsd:string" />
    <wsdl:part name="filetype" type="xsd:int" />
  </wsdl:message>
  <wsdl:message name="employeeDoorsReportResponse">
    <wsdl:part name="employeeDoorsReporttReturn" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="heartBeat">
    <wsdl:part name="sessionid" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="heartBeatResponse">
    <wsdl:part name="heartBeatReturn" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="searchDoorsCards">
    <wsdl:part name="sessionid" type="xsd:string" />
    <wsdl:part name="doors" type="xsd:string" />
  </wsdl:message>
  <wsdl:message name="searchDoorsCardsResponse">
    <wsdl:part name="searchDoorsCardsReturn" type="xsd:string" />
  </wsdl:message>
  <wsdl:portType name="ServicePortType">
    <wsdl:operation name="login">
      <wsdl:input message="typens:login" />
      <wsdl:output message="typens:loginResponse" />
    </wsdl:operation>
    <wsdl:operation name="insertReserveCards">
      <wsdl:input message="typens:insertReserveCards" />
      <wsdl:output message="typens:insertReserveCardsResponse" />
    </wsdl:operation>
    <wsdl:operation name="searchReserveCards">
      <wsdl:input message="typens:searchReserveCards" />
      <wsdl:output message="typens:searchReserveCardsResponse" />
    </wsdl:operation>
    <wsdl:operation name="deleteReserveCards">
      <wsdl:input message="typens:deleteReserveCards" />
      <wsdl:output message="typens:deleteReserveCardsResponse" />
    </wsdl:operation>
    <wsdl:operation name="insertPublicDoors">
      <wsdl:input message="typens:insertPublicDoors" />
      <wsdl:output message="typens:insertPublicDoorsResponse" />
    </wsdl:operation>
    <wsdl:operation name="updatePublicDoors">
      <wsdl:input message="typens:updatePublicDoors" />
      <wsdl:output message="typens:updatePublicDoorsResponse" />
    </wsdl:operation>
    <wsdl:operation name="searchPublicDoors">
      <wsdl:input message="typens:searchPublicDoors" />
      <wsdl:output message="typens:searchPublicDoorsResponse" />
    </wsdl:operation>
    <wsdl:operation name="deletePublicDoors">
      <wsdl:input message="typens:deletePublicDoors" />
      <wsdl:output message="typens:deletePublicDoorsResponse" />
    </wsdl:operation>
    <wsdl:operation name="updateDoors">
      <wsdl:input message="typens:updateDoors" />
      <wsdl:output message="typens:updateDoorsResponse" />
    </wsdl:operation>
    <wsdl:operation name="insertEmployees">
      <wsdl:input message="typens:insertEmployees" />
      <wsdl:output message="typens:insertEmployeesResponse" />
    </wsdl:operation>
    <wsdl:operation name="deleteEmployees">
      <wsdl:input message="typens:deleteEmployees" />
      <wsdl:output message="typens:deleteEmployeesResponse" />
    </wsdl:operation>
    <wsdl:operation name="updateEmployees">
      <wsdl:input message="typens:updateEmployees" />
      <wsdl:output message="typens:updateEmployeesResponse" />
    </wsdl:operation>
    <wsdl:operation name="searchEmployees">
      <wsdl:input message="typens:searchEmployees" />
      <wsdl:output message="typens:searchEmployeesResponse" />
    </wsdl:operation>
    <wsdl:operation name="insertCards">
      <wsdl:input message="typens:insertCards" />
      <wsdl:output message="typens:insertCardsResponse" />
    </wsdl:operation>
    <wsdl:operation name="updateCards">
      <wsdl:input message="typens:updateCards" />
      <wsdl:output message="typens:updateCardsResponse" />
    </wsdl:operation>
    <wsdl:operation name="deleteCards">
      <wsdl:input message="typens:deleteCards" />
      <wsdl:output message="typens:deleteCardsResponse" />
    </wsdl:operation>
    <wsdl:operation name="searchCards">
      <wsdl:input message="typens:searchCards" />
      <wsdl:output message="typens:searchCardsResponse" />
    </wsdl:operation>
    <wsdl:operation name="insertDepts">
      <wsdl:input message="typens:insertDepts" />
      <wsdl:output message="typens:insertDeptsResponse" />
    </wsdl:operation>
    <wsdl:operation name="updateDepts">
      <wsdl:input message="typens:updateDepts" />
      <wsdl:output message="typens:updateDeptsResponse" />
    </wsdl:operation>
    <wsdl:operation name="deleteDepts">
      <wsdl:input message="typens:deleteDepts" />
      <wsdl:output message="typens:deleteDeptsResponse" />
    </wsdl:operation>
    <wsdl:operation name="searchDepts">
      <wsdl:input message="typens:searchDepts" />
      <wsdl:output message="typens:searchDeptsResponse" />
    </wsdl:operation>
    <wsdl:operation name="getCardsDoors">
      <wsdl:input message="typens:getCardsDoors" />
      <wsdl:output message="typens:getCardsDoorsResponse" />
    </wsdl:operation>
    <wsdl:operation name="searchCardsDoors">
      <wsdl:input message="typens:searchCardsDoors" />
      <wsdl:output message="typens:searchCardsDoorsResponse" />
    </wsdl:operation>
    <wsdl:operation name="asyncSetCardsDoors">
      <wsdl:input message="typens:asyncSetCardsDoors" />
      <wsdl:output message="typens:asyncSetCardsDoorsResponse" />
    </wsdl:operation>
    <wsdl:operation name="setCardsDoors">
      <wsdl:input message="typens:setCardsDoors" />
      <wsdl:output message="typens:setCardsDoorsResponse" />
    </wsdl:operation>
    <wsdl:operation name="normalCardeventReport">
      <wsdl:input message="typens:normalCardeventReport" />
      <wsdl:output message="typens:normalCardeventReportResponse" />
    </wsdl:operation>
    <wsdl:operation name="abnormalCardeventReport">
      <wsdl:input message="typens:abnormalCardeventReport" />
      <wsdl:output message="typens:abnormalCardeventReportResponse" />
    </wsdl:operation>
    <wsdl:operation name="doorRelationReport">
      <wsdl:input message="typens:doorRelationReport" />
      <wsdl:output message="typens:doorRelationReportResponse" />
    </wsdl:operation>
    <wsdl:operation name="abnormalEmployeeeventReport">
      <wsdl:input message="typens:abnormalEmployeeeventReport" />
      <wsdl:output message="typens:abnormalEmployeeeventReportResponse" />
    </wsdl:operation>
    <wsdl:operation name="employeeAuthReport">
      <wsdl:input message="typens:employeeAuthReport" />
      <wsdl:output message="typens:employeeAuthReportResponse" />
    </wsdl:operation>
    <wsdl:operation name="doorReport">
      <wsdl:input message="typens:doorReport" />
      <wsdl:output message="typens:doorReportResponse" />
    </wsdl:operation>
    <wsdl:operation name="accessLabReport">
      <wsdl:input message="typens:accessLabReport" />
      <wsdl:output message="typens:accessLabReportResponse" />
    </wsdl:operation>
    <wsdl:operation name="employeeDoorsReport">
      <wsdl:input message="typens:employeeDoorsReport" />
      <wsdl:output message="typens:employeeDoorsReportResponse" />
    </wsdl:operation>
    <wsdl:operation name="heartBeat">
      <wsdl:input message="typens:heartBeat" />
      <wsdl:output message="typens:heartBeatResponse" />
    </wsdl:operation>
    <wsdl:operation name="searchDoorsCards">
      <wsdl:input message="typens:searchDoorsCards" />
      <wsdl:output message="typens:searchDoorsCardsResponse" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="ServiceBinding" type="typens:ServicePortType">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" style="rpc" />
    <wsdl:operation name="login">
      <soap:operation soapAction="urn:ServiceAction" />
      <wsdl:input>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="insertReserveCards">
      <soap:operation soapAction="urn:ServiceAction" />
      <wsdl:input>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="searchReserveCards">
      <soap:operation soapAction="urn:ServiceAction" />
      <wsdl:input>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="deleteReserveCards">
      <soap:operation soapAction="urn:ServiceAction" />
      <wsdl:input>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="insertPublicDoors">
      <soap:operation soapAction="urn:ServiceAction" />
      <wsdl:input>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="updatePublicDoors">
      <soap:operation soapAction="urn:ServiceAction" />
      <wsdl:input>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="searchPublicDoors">
      <soap:operation soapAction="urn:ServiceAction" />
      <wsdl:input>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="deletePublicDoors">
      <soap:operation soapAction="urn:ServiceAction" />
      <wsdl:input>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="updateDoors">
      <soap:operation soapAction="urn:ServiceAction" />
      <wsdl:input>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="insertEmployees">
      <soap:operation soapAction="urn:ServiceAction" />
      <wsdl:input>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="deleteEmployees">
      <soap:operation soapAction="urn:ServiceAction" />
      <wsdl:input>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="updateEmployees">
      <soap:operation soapAction="urn:ServiceAction" />
      <wsdl:input>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="searchEmployees">
      <soap:operation soapAction="urn:ServiceAction" />
      <wsdl:input>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="insertCards">
      <soap:operation soapAction="urn:ServiceAction" />
      <wsdl:input>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="updateCards">
      <soap:operation soapAction="urn:ServiceAction" />
      <wsdl:input>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="deleteCards">
      <soap:operation soapAction="urn:ServiceAction" />
      <wsdl:input>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="searchCards">
      <soap:operation soapAction="urn:ServiceAction" />
      <wsdl:input>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="insertDepts">
      <soap:operation soapAction="urn:ServiceAction" />
      <wsdl:input>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="updateDepts">
      <soap:operation soapAction="urn:ServiceAction" />
      <wsdl:input>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="deleteDepts">
      <soap:operation soapAction="urn:ServiceAction" />
      <wsdl:input>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="searchDepts">
      <soap:operation soapAction="urn:ServiceAction" />
      <wsdl:input>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getCardsDoors">
      <soap:operation soapAction="urn:ServiceAction" />
      <wsdl:input>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="searchCardsDoors">
      <soap:operation soapAction="urn:ServiceAction" />
      <wsdl:input>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="asyncSetCardsDoors">
      <soap:operation soapAction="urn:ServiceAction" />
      <wsdl:input>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="setCardsDoors">
      <soap:operation soapAction="urn:ServiceAction" />
      <wsdl:input>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="normalCardeventReport">
      <soap:operation soapAction="urn:ServiceAction" />
      <wsdl:input>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="abnormalCardeventReport">
      <soap:operation soapAction="urn:ServiceAction" />
      <wsdl:input>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="doorRelationReport">
      <soap:operation soapAction="urn:ServiceAction" />
      <wsdl:input>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="abnormalEmployeeeventReport">
      <soap:operation soapAction="urn:ServiceAction" />
      <wsdl:input>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="employeeAuthReport">
      <soap:operation soapAction="urn:ServiceAction" />
      <wsdl:input>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="doorReport">
      <soap:operation soapAction="urn:ServiceAction" />
      <wsdl:input>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="accessLabReport">
      <soap:operation soapAction="urn:ServiceAction" />
      <wsdl:input>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="employeeDoorsReport">
      <soap:operation soapAction="urn:ServiceAction" />
      <wsdl:input>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="heartBeat">
      <soap:operation soapAction="urn:ServiceAction" />
      <wsdl:input>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="searchDoorsCards">
      <soap:operation soapAction="urn:ServiceAction" />
      <wsdl:input>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="encoded" namespace="urn:wsdl" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="wsdlService">
    <wsdl:port name="ServicePort" binding="typens:ServiceBinding">
      <soap:address location="http://127.0.0.1/nav/system/employee/soapserver" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>