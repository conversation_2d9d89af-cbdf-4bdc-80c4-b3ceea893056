using CtrEmail.Utils;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Xml;

namespace CtrEmail
{
    public static class Webservice
    {
        public static WebReference.wsdlService WsdlService = new WebReference.wsdlService();
        static Webservice()
        {
            WsdlService.Timeout = 10*60*1000; // 设置超时

            //WsdlService.loginCompleted += new  CtrEmail.WebReference.loginCompletedEventHandler(WsdlService_loginCompleted);
            //WsdlService.loginCompleted += WsdlService_loginCompleted;
        }

        /*private static void WsdlService_loginCompleted(object sender, WebReference.loginCompletedEventArgs e)
        {
            try
            {

                App app = Application.Current as App;
                String loginResultStr = e.Result;
                String serverip = (String)e.UserState;
                serverip = serverip.Substring(0, serverip.IndexOf('&'));
                XmlDocument xmlParser = new XmlDocument();
                xmlParser.LoadXml(loginResultStr);
                string rvalue = xmlParser.SelectSingleNode("result/rvalue").InnerText;
                if (rvalue.Equals("1"))
                {
                    String sessionStr = xmlParser.GetElementsByTagName("sessionid").Item(0).ChildNodes[0].InnerText;
                    app.ensServer.Session = sessionStr;
                }
            }
            catch (Exception)
            {
            }
        }*/

        public static string login(ViewModel.EnsItem ensServer)
        {
            try
            {
                //App app = Application.Current as App;
                string loginResultStr = "";
                try
                {
                    updateUrl(ensServer.Ip);
                    loginResultStr = WsdlService.login(ensServer.Username, Utils.Util.md5( ensServer.Password));
                }
                catch (Exception ex)
                {
                    EnsLog.GetInstance().Write("login fail1:", ex);
                    System.Diagnostics.Debug.Print(ex.Message);
                    return "";
                }
                if (String.IsNullOrEmpty(loginResultStr))
                {
                    return "";
                }
                XmlDocument xmlParser = new XmlDocument();
                xmlParser.LoadXml(loginResultStr);
                string rvalue = xmlParser.SelectSingleNode("result/rvalue").InnerText;
                if (rvalue.Equals("1"))
                {
                    String sessionStr = xmlParser.GetElementsByTagName("sessionid").Item(0).ChildNodes[0].InnerText;
                    //ensServer.Session = sessionStr;

                    return sessionStr;
                }
                else
                {
                    return "";
                }
            }
            catch (Exception ex)
            {
                EnsLog.GetInstance().Write("login fail2", ex);
                return "";
            }

        }

        //public static ObservableCollection<Controller> controllerList = new ObservableCollection<Controller>();

        public static bool  GetControllers(ViewModel.EnsItem ensServer, ref ObservableCollection<Controller> controllerList)
        {

            try
            {
                updateUrl(ensServer.Ip);

                App app = Application.Current as App;
                MainWindow mainWin = app.mainWin;
                if (mainWin == null || ensServer == null || string.IsNullOrEmpty(ensServer.Session))
                {
                    return false;
                }

                XmlDocument xmlParser = new XmlDocument();
                for (int i = 0; i < 3; i++)
                {
                    string ctrlistStr = WsdlService.getCtrs(ensServer.Session);
                    xmlParser.LoadXml(ctrlistStr);
                    if ("2".Equals(xmlParser.SelectSingleNode("result/rvalue").InnerText))  // 未登录或session超时
                    {
                        ensServer.Session = WsdlService.login(ensServer.Username, ensServer.Password);
                    }
                    else
                    {
                        break;
                    }
                }


                if ("1".Equals(xmlParser.SelectSingleNode("result/rvalue").InnerText))
                {
                    controllerList.Clear();

                    XmlNodeList ctrList = xmlParser.GetElementsByTagName("controller");
                    foreach (XmlNode ctr in ctrList)
                    {
                        int ctrid = 0;
                        Int32.TryParse(ctr.SelectSingleNode("ctrid").InnerText,out ctrid);
                        int typeid = 0;
                        Int32.TryParse(ctr.SelectSingleNode("ctrtype").InnerText, out typeid);
                        string ip = ctr.SelectSingleNode("ip").InnerText;
                        int ctraddress = 0;
                        Int32.TryParse(ctr.SelectSingleNode("ctraddress").InnerText,out ctraddress);
                        string location = ctr.SelectSingleNode("location").InnerText;
                        string username = ctr.SelectSingleNode("username").InnerText;
                        string password = ctr.SelectSingleNode("password").InnerText;

                        Controller controller = new Controller();

                        controller.Id = ctrid;
                        controller.Typeid = typeid;
                        controller.Ipaddress = ip;
                        controller.Ctraddress = ctraddress;
                        controller.Location = location;
                        controller.Username = username;
                        controller.Password = password;

                        controllerList.Add(controller);
                            
                    }
                }

                //MessageBox.Show(ctrlistStr);
                return true;
            }
            catch(Exception ex)
            {
                EnsLog.GetInstance().Write("Webservice->GetControllers:" + ex.Message,ex);
                return false;
            }
            
        }

        public static string GetStatus(ViewModel.EnsItem ensServer, ref ObservableCollection<Controller> controllerList)
        {

            try
            {
                updateUrl(ensServer.Ip);
                App app = Application.Current as App;
                MainWindow mainWin = app.mainWin;
                if (mainWin == null || ensServer == null || string.IsNullOrEmpty(ensServer.Session))
                {
                    return "";
                }
                //string ctrlistStr = WsdlService.heartBeat(ensServer.Session);
                string webresultStr = WsdlService.getStatus(ensServer.Session);
                XmlDocument xmlParser = new XmlDocument();
                xmlParser.LoadXml(webresultStr);
                if(xmlParser.HasChildNodes)
                {

                    XmlNodeList ctrList = xmlParser.GetElementsByTagName("status");
                    foreach (XmlNode ctr in ctrList)
                    {
                        for(int i = 0; i < controllerList.Count; i++)
                        {
                            string ctrnoStr = ctr.SelectSingleNode("ctrno").InnerText;
                            string onlineStr = ctr.SelectSingleNode("online").InnerText;
                            int ctraddressInt = controllerList[i].Ctraddress;
                            if(string.IsNullOrEmpty(ctrnoStr) || ctraddressInt == 0)    // 跳过无效的
                            {
                                continue;
                            }
                            int ctrnoInt = 0;
                            Int32.TryParse(ctrnoStr, out ctrnoInt);
                            int onlineInt = 0;
                            Int32.TryParse(onlineStr, out onlineInt);
                            if(ctrnoInt == ctraddressInt)
                            {
                                if(onlineInt == 0)
                                {
                                    controllerList[i].Status = "离线";
                                }
                                else
                                {
                                    controllerList[i].Status = "在线";
                                }
                            }
                        }

                        /*int ctrid = 0;
                        Int32.TryParse(ctr.SelectSingleNode("ctrid").InnerText, out ctrid);
                        int typeid = 0;
                        Int32.TryParse(ctr.SelectSingleNode("ctrtype").InnerText, out typeid);
                        string ip = ctr.SelectSingleNode("ip").InnerText;
                        int ctraddress = 0;
                        Int32.TryParse(ctr.SelectSingleNode("ctraddress").InnerText, out ctraddress);
                        string location = ctr.SelectSingleNode("location").InnerText;
                        string username = ctr.SelectSingleNode("username").InnerText;
                        string password = ctr.SelectSingleNode("password").InnerText;
                        */
                    }
                }

                //MessageBox.Show(ctrlistStr);
                return "";
            }
            catch (Exception ex)
            {
                EnsLog.GetInstance().Write("Webservice->GetControllers:" + ex.Message, ex);
                return "";
            }

        }

        //更新url
        public static bool updateUrl(String serverip)
        {
            if (!String.IsNullOrEmpty(serverip))
            {
                if (serverip.Equals(WsdlService.Url))
                {
                    return true;
                }

                StringBuilder sb = new StringBuilder();
                if (!serverip.StartsWith("http://"))
                {
                    sb.Append("http://");
                }
                sb.Append(serverip);
                sb.Append("/nav/system/employee/soapserver?wsdl");

                WsdlService.Url = sb.ToString();
                return true;
            }
            else
            {
                return false;
            }
        }


        public static string GetTimeStamp()
        {
            try
            {
                TimeSpan ts = DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, 0);
                return Convert.ToInt64(ts.TotalSeconds).ToString();
            }
            catch (Exception)
            {
                return "0";
            }
        }

        public static bool GetCtrCards(ViewModel.EnsItem ensServer, int ctraddressInt,ref int cardindexInt,ref ObservableCollection<CardAuth> cardauthList)
        {

            try
            {
                
                updateUrl(ensServer.Ip);
                App app = Application.Current as App;
                MainWindow mainWin = app.mainWin;
                if (mainWin == null || ensServer == null || string.IsNullOrEmpty(ensServer.Session))
                {
                    return false;
                }

                XmlDocument xmlParser = new XmlDocument();
                for (int i = 0; i < 3; i++)
                {
                    string ctrargStr = string.Format("<ctr><ctraddress>{0}</ctraddress><cardindex>{1}</cardindex></ctr>", ctraddressInt, cardindexInt);
                    string ctrlistStr = WsdlService.getCtrCards(ensServer.Session, ctrargStr);
                    
                    xmlParser.LoadXml(ctrlistStr);
                    if ("2".Equals(xmlParser.SelectSingleNode("result/rvalue").InnerText))  // 未登录或session超时
                    {
                        ensServer.Session = WsdlService.login(ensServer.Username,ensServer.Password);
                    }
                    else
                    {
                        break;
                    }
                }

                string tempstr = xmlParser.SelectSingleNode("result/rvalue").InnerText;
                

                if ("1".Equals(xmlParser.SelectSingleNode("result/rvalue").InnerText))
                {
                    XmlNodeList cardnodeList = xmlParser.GetElementsByTagName("card");
                    foreach (XmlNode cardnode in cardnodeList)
                    {
                        CardAuth cardAuth = new CardAuth();

                        cardAuth.Cardno = cardnode.SelectSingleNode("cardno").InnerText;
                        
                        XmlNode doorsNode = cardnode.SelectSingleNode("doors");
                        XmlNodeList doornodeList = doorsNode != null ? doorsNode.SelectNodes("door") : null;
                        List<Door> alldoorList = new List<Door>();
                        foreach (XmlNode doornode in doornodeList)
                        {
                            Door door = new Door();
                            
                            int doorxs = 0;
                            int doorid = 0;
                            int.TryParse(doornode.SelectSingleNode("doorid").InnerText, out doorid);
                            door.Doorid = doorid;
                            door.Location = doornode.SelectSingleNode("doorlocation").InnerText;

                            bool hasdoorxsBl = int.TryParse(doornode.SelectSingleNode("doorxs").InnerText, out doorxs);
                            if (hasdoorxsBl)
                            {
                                if(doorxs == 65535)
                                {
                                    alldoorList.Add(door);
                                }

                                if(doorxs != 32767) //没有权限
                                {
                                    continue;
                                }
                            }
                            else
                            {
                                continue;
                            }

                            
                            cardAuth.Doors.Add(door);   // 插入有权限的门
                        }
                        if (cardAuth.Doors.Count > 0)   // 有门权限，才加入
                        {
                            cardauthList.Add(cardAuth);
                        }
                        if(alldoorList.Count == doornodeList.Count) // 具有全部门权限，没有启用门计划
                        {
                            foreach(Door door in alldoorList)
                            {
                                cardAuth.Doors.Add(door);
                            }
                            cardauthList.Add(cardAuth);
                        }
                        
                    }
                }
                //MessageBox.Show(ctrlistStr);
                string cardindexStr = xmlParser.SelectSingleNode("result/cardindex").InnerText;
                int getcardindexInt = 0;
                if(Int32.TryParse(cardindexStr,out getcardindexInt))
                {
                    cardindexInt = getcardindexInt;
                }
                return true;
            }
            catch (Exception ex)
            {
                EnsLog.GetInstance().Write("Webservice->GetControllers:" + ex.Message, ex);
                return false;
            }

        }

        public static bool GetDbCards(ViewModel.EnsItem ensServer, int ctraddressInt, ref ObservableCollection<CardAuth> dbcardauthList)
        {

            try
            {
                updateUrl(ensServer.Ip);
                App app = Application.Current as App;
                MainWindow mainWin = app.mainWin;
                if (mainWin == null || ensServer == null || string.IsNullOrEmpty(ensServer.Session))
                {
                    return false;
                }

                XmlDocument xmlParser = new XmlDocument();
                for (int i = 0; i < 3; i++)
                {
                    string ctrlistStr = WsdlService.getDbCards(ensServer.Session, ctraddressInt);

                    xmlParser.LoadXml(ctrlistStr);
                    if ("2".Equals(xmlParser.SelectSingleNode("result/rvalue").InnerText))  // 未登录或session超时
                    {
                        ensServer.Session = WsdlService.login(ensServer.Username, ensServer.Password);
                    }
                    else
                    {
                        break;
                    }
                }

                string tempstr = xmlParser.SelectSingleNode("result/rvalue").InnerText;

                if ("1".Equals(xmlParser.SelectSingleNode("result/rvalue").InnerText))
                {
                    dbcardauthList.Clear();

                    XmlNodeList cardnodeList = xmlParser.GetElementsByTagName("card");
                    foreach (XmlNode cardnode in cardnodeList)
                    {
                        CardAuth cardAuth = new CardAuth();

                        cardAuth.Cardno = cardnode.SelectSingleNode("cardno").InnerText;

                        XmlNode doorsNode2 = cardnode.SelectSingleNode("doors");
                        XmlNodeList doornodeList = doorsNode2 != null ? doorsNode2.SelectNodes("door") : null;

                        foreach (XmlNode doornode in doornodeList)
                        {
                            Door door = new Door();

                            int doorid = 0;
                            int.TryParse(doornode.SelectSingleNode("doorid").InnerText, out doorid);
                            door.Doorid = doorid;
                            door.Location = doornode.SelectSingleNode("doorlocation").InnerText;
                            cardAuth.Doors.Add(door);   // 插入有权限的门
                        }
                        if (cardAuth.Doors.Count > 0)   // 有门权限，才加入
                        {
                            dbcardauthList.Add(cardAuth);
                        }

                    }
                }
                //MessageBox.Show(ctrlistStr);
                return true;
            }
            catch (Exception ex)
            {
                EnsLog.GetInstance().Write("Webservice->GetControllers:" + ex.Message, ex);
                return false;
            }

        }


        public static bool AddDbCardAuth(ViewModel.EnsItem ensServer, ObservableCollection<CardAuth> cardAuths)
        {
            try
            {
                App app = Application.Current as App;
                MainWindow mainWin = app.mainWin;
                updateUrl(ensServer.Ip);
                if (mainWin == null || ensServer == null || string.IsNullOrEmpty(ensServer.Session))
                {
                    return false;
                }
                StringBuilder sb = new StringBuilder();
                sb.Append("<cards>");
                foreach (CardAuth cardAuth in cardAuths)
                {
                    sb.Append("<card>");
                    sb.Append("<cardno>");
                    sb.Append(cardAuth.Cardno);
                    sb.Append("</cardno>");
                    sb.Append("<doors>");
                    foreach (Door door in cardAuth.Doors)
                    {
                        sb.Append("<door>");
                        sb.Append("<doorid>");
                        sb.Append(door.Doorid);
                        sb.Append("</doorid><doorlocation>");
                        sb.Append(door.Location);
                        sb.Append("</doorlocation>");
                        sb.Append("</door>");
                    }

                    sb.Append("</doors></card>");
                }
                sb.Append("</cards>");
                string cardsStr = sb.ToString();

                XmlDocument xmlParser = new XmlDocument();
                for (int i = 0; i < 3; i++)
                {
                    string addDbCardResultStr = WsdlService.setDbCards(ensServer.Session, cardsStr);

                    xmlParser.LoadXml(addDbCardResultStr);
                    if ("2".Equals(xmlParser.SelectSingleNode("result/rvalue").InnerText))  // 未登录或session超时
                    {
                        ensServer.Session = WsdlService.login(ensServer.Username, ensServer.Password);
                    }
                    else
                    {
                        break;
                    }
                }

                string tempstr = xmlParser.SelectSingleNode("result/rvalue").InnerText;

                if ("1".Equals(xmlParser.SelectSingleNode("result/rvalue").InnerText))
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            catch (Exception)
            {
                return false;
            }
        } 
    }
}
